---
description: 
globs: 
alwaysApply: true
---
# 代码开发规范
- 使用Java进行项目代码开发
- 函数提供简洁注释
- 保证性能和代码可读性
- 遵守下面规则：
    - SOLID原则
    - DRY原则(Don't Repeat Yourself)
    - KISS原则(Keep It Simple, Stupid)
    - YAGNI原则(You Aren't Gonna Need It)


# 代码风格
- 遵循阿里巴巴Java开发手册规范
- 方法体不超过50行,类不超过500行
- 遵循 clean code 原则,保持代码整洁
- 重要代码必须添加注释说明


# 技术栈
- 框架：Spring Boot 3 + Maven
- Java版本：Java 8
- 核心依赖：Spring Web、MySQL、Lombok、Squirrel、Mafka、Thrift、Mybatis等