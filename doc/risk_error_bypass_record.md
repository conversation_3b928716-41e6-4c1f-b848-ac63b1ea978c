# 错误绕行检测记录表实现计划

## 前言
根据工程结构分析，实现错误绕行检测记录表需要按照既有的检测器模式进行开发。参考了逆行预检过程(`RiskRetrogradeRecord`)的实现方式，以下是详细的计划。

## 数据库

已有建表SQL:
```sql
CREATE TABLE `risk_error_bypass_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `tmp_case_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '临时事件ID，构造方式{vehicleId}{yyyyMMddHHmmss}S{source}T{type}',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '风险事件类型',
  `vin` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '车辆VIN码',
  `duration` int(11) NOT NULL DEFAULT '0' COMMENT '持续时长(s)',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态，0-未停滞|10-确认中|20-已确认|99-已取消|100-超时取消（超过一定时被系统取消）',
  `vehicle_runtime_info_snapshot` text COLLATE utf8mb4_unicode_ci COMMENT '事件召回时车辆运行信息状态快照',
  `occur_time` timestamp NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '停滞开始时间',
  `recall_time` timestamp NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '停滞召回时间',
  `close_time` timestamp NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '停滞解除时间',
  `ext_info` text COLLATE utf8mb4_unicode_ci COMMENT '扩展信息(JSON格式)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `stagnation_counter` text COLLATE utf8mb4_unicode_ci COMMENT '停滞积累信息',
  `is_cross_line` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否压线 0-否 1-是',
  `is_in_route_lane` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否在路由规划车道 0-否 1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tmp_case_id` (`tmp_case_id`),
  KEY `idx_vin` (`vin`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_vin_status` (`vin`,`status`) COMMENT '车辆和状态的联合索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='错误绕行检测记录表'
```

## 需要新增的文件

### 1. 实体类（PO）
- 路径：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/dal/po/eve/riskcenter/RiskErrorBypassRecord.java`
- 描述：对应数据库表的持久化对象

### 2. 领域对象（DO）
- 路径：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/model/core/RiskErrorBypassRecordDO.java`
- 描述：领域对象，继承自`RiskDetectorRecordBaseDO`

### 3. Mapper接口
- 路径：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/dal/mapper/eve/riskcenter/RiskErrorBypassRecordMapper.java`
- 描述：DAO层Mapper接口，继承自`CommonMapper<RiskErrorBypassRecord>`

### 4. 转换器
- 路径：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/convert/RiskErrorBypassRecordConvert.java`
- 描述：PO与DO之间的转换器

### 5. DTO查询参数
- 路径：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/repository/dbrepo/dto/RiskErrorBypassRecordDOQueryParamDTO.java`
- 描述：查询参数DTO

### 6. 仓储接口
- 路径：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/repository/dbrepo/RiskErrorBypassRecordRepository.java`
- 描述：定义仓储操作接口

### 7. 仓储实现
- 路径：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/repository/dbrepo/impl/RiskErrorBypassRecordRepositoryImpl.java`
- 描述：实现仓储接口

### 8. 工厂类
- 路径：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/factory/riskdetector/RiskErrorBypassRecordFactory.java`
- 描述：用于初始化错误绕行记录对象

### 9. 检测器实现
- 路径：`wallemonitor-risk-center-domain/src/main/java/com/sankuai/wallemonitor/risk/center/domain/strategy/detector/impl/ErrorBypassDetector.java`
- 描述：实现错误绕行检测逻辑

## 需要修改的文件

### 1. 风险事件类型枚举
- 路径：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/enums/RiskCaseTypeEnum.java`
- 修改内容：添加错误绕行类型枚举值
```java
ERROR_BYPASS(13, "错误绕行", new HashSet<>()),
```

## 具体实现细节

### 1. RiskErrorBypassRecord.java (PO)
```java
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("risk_error_bypass_record")
public class RiskErrorBypassRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 临时事件ID，构造方式{vehicleId}{yyyyMMddHHmmss}S{source}T{type}
     */
    @TableField("tmp_case_id")
    @TableUnique
    private String tmpCaseId;

    /**
     * 风险事件类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 车辆VIN码
     */
    @TableField("vin")
    private String vin;

    /**
     * 持续时长(s)
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 状态，0-未停滞|10-确认中|20-已确认|99-已取消|100-超时取消（超过一定时被系统取消）
     */
    @TableField("status")
    private Integer status;

    /**
     * 事件召回时车辆运行信息状态快照
     */
    @TableField("vehicle_runtime_info_snapshot")
    private String vehicleRuntimeInfoSnapshot;

    /**
     * 停滞开始时间
     */
    @TableField("occur_time")
    private Date occurTime;

    /**
     * 停滞召回时间
     */
    @TableField("recall_time")
    private Date recallTime;

    /**
     * 停滞解除时间
     */
    @TableField("close_time")
    private Date closeTime;

    /**
     * 扩展信息(JSON格式)
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 停滞积累信息
     */
    @TableField("stagnation_counter")
    private String stagnationCounter;

    /**
     * 是否压线 0-否 1-是
     */
    @TableField("is_cross_line")
    private Boolean isCrossLine;

    /**
     * 是否在路由规划车道 0-否 1-是
     */
    @TableField("is_in_route_lane")
    private Boolean isInRouteLane;
}
```

### 2. RiskErrorBypassRecordDO.java (DO)
```java
@Data
@SuperBuilder
@NoArgsConstructor
public class RiskErrorBypassRecordDO extends RiskDetectorRecordBaseDO {
    
    /**
     * 是否压线 0-否 1-是
     */
    private Boolean isCrossLine;

    /**
     * 是否在路由规划车道 0-否 1-是
     */
    private Boolean isInRouteLane;
}
```

### 3. RiskErrorBypassRecordMapper.java
```java
public interface RiskErrorBypassRecordMapper extends CommonMapper<RiskErrorBypassRecord> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<RiskErrorBypassRecord> getPOClass() {
        return RiskErrorBypassRecord.class;
    }
}
```

### 4. RiskErrorBypassRecordConvert.java
```java
@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskErrorBypassRecordConvert extends SingleConvert<RiskErrorBypassRecord, RiskErrorBypassRecordDO> {

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusEnum")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseTypeEnum")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "parseVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "parseVehicleRuntimeInfoContextDO")
    RiskErrorBypassRecordDO toDO(RiskErrorBypassRecord riskErrorBypassRecord);

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusInteger")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseType")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "serializeVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "serializeVehicleRuntimeInfoContextDO")
    RiskErrorBypassRecord toPO(RiskErrorBypassRecordDO riskErrorBypassRecordDO);
}
```

### 5. RiskErrorBypassRecordDOQueryParamDTO.java
```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RiskErrorBypassRecordDOQueryParamDTO {

    private String tmpCaseId;

    @InQuery(field = "tmpCaseId")
    private List<String> tmpCaseIdList;

    private String vin;

    @InQuery(field = "vin")
    private List<String> vinList;

    private Integer duration;

    private Integer status;

    @InQuery(field = "status")
    private List<Integer> statusList;

    @RangeQuery(field = "occurTime")
    private TimePeriod occurTimeRange;

    @RangeQuery(field = "recallTime")
    private TimePeriod recallTimeRange;

    @RangeQuery(field = "closeTime")
    private TimePeriod closeTimeRange;

    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    @RangeQuery(field = "updateTime")
    private TimePeriod updateTimeRange;

    @OrderBy(field = "createTime")
    private OrderEnum orderByCreateTime;

    private Boolean isCrossLine;

    private Boolean isInRouteLane;

    @Builder.Default
    private Boolean isDeleted = false;
}
```

### 6. RiskErrorBypassRecordRepository.java
```java
public interface RiskErrorBypassRecordRepository {

    /**
     * 根据参数查询错误绕行检测记录
     *
     * @param paramDTO 查询参数
     * @return 错误绕行检测记录DO列表
     */
    List<RiskErrorBypassRecordDO> queryByParam(RiskErrorBypassRecordDOQueryParamDTO paramDTO);

    /**
     * 根据参数分页查询错误绕行检测记录
     *
     * @param paramDTO 查询参数
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 分页对象
     */
    Paging<RiskErrorBypassRecordDO> queryByParamByPage(RiskErrorBypassRecordDOQueryParamDTO paramDTO, Integer pageNum,
            Integer pageSize);

    /**
     * 根据临时事件ID查询错误绕行检测记录
     *
     * @param tmpCaseId 临时事件ID
     * @return 错误绕行检测记录DO
     */
    RiskErrorBypassRecordDO getByTmpCaseId(String tmpCaseId);

    /**
     * 保存错误绕行检测记录
     *
     * @param riskErrorBypassRecordDO 错误绕行检测记录DO
     */
    void save(RiskErrorBypassRecordDO riskErrorBypassRecordDO);

    /**
     * 批量保存错误绕行检测记录
     *
     * @param riskErrorBypassRecordDOList 错误绕行检测记录DO列表
     */
    void batchSave(List<RiskErrorBypassRecordDO> riskErrorBypassRecordDOList);
}
```

### 7. RiskErrorBypassRecordRepositoryImpl.java
```java
@Component
@Slf4j
public class RiskErrorBypassRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskErrorBypassRecordMapper, RiskErrorBypassRecordConvert, RiskErrorBypassRecord, RiskErrorBypassRecordDO> implements
        RiskErrorBypassRecordRepository {

    private static final String UK_TMP_CASE_ID = "tmpCaseId";

    /**
     * 根据参数查询错误绕行检测记录
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskErrorBypassRecordDO> queryByParam(RiskErrorBypassRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数查询错误绕行检测记录 (分页)
     *
     * @param paramDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public Paging<RiskErrorBypassRecordDO> queryByParamByPage(RiskErrorBypassRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据临时事件ID查询错误绕行检测记录
     *
     * @param tmpCaseId
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskErrorBypassRecordDO getByTmpCaseId(String tmpCaseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_TMP_CASE_ID)
                .value(tmpCaseId)
                .build()));
    }

    /**
     * 保存错误绕行检测记录
     *
     * @param riskErrorBypassRecordDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskErrorBypassRecordDO riskErrorBypassRecordDO) {
        super.save(riskErrorBypassRecordDO);
    }

    /**
     * 批量保存错误绕行检测记录
     *
     * @param riskErrorBypassRecordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskErrorBypassRecordDO> riskErrorBypassRecordDOList) {
        super.batchSave(riskErrorBypassRecordDOList);
    }
}
```

### 8. RiskErrorBypassRecordFactory.java
```java
@Component
public class RiskErrorBypassRecordFactory extends RiskDetectorRecordFactory<RiskErrorBypassRecordDO> {

    @Resource
    private IDGenerateRepository idGenerateRepository;


    @Override
    public RiskErrorBypassRecordDO init(DetectContextDTO detectContextDTO) {
        VehicleRuntimeInfoContextDO runtimeContextDO = detectContextDTO.getRuntimeContext();
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, runtimeContextDO.getVin(),
                RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.ERROR_BYPASS, runtimeContextDO.getLastUpdateTime());
        return RiskErrorBypassRecordDO.builder()
                .tmpCaseId(caseId)
                .type(RiskCaseTypeEnum.ERROR_BYPASS)
                .vin(runtimeContextDO.getVin())
                .duration(0)
                .status(DetectRecordStatusEnum.PROCESSING)
                .occurTime(runtimeContextDO.getLastUpdateTime())
                .isCrossLine(runtimeContextDO.getIsCrossLine())
                .isInRouteLane(runtimeContextDO.getIsInRouteLane())
                .build();
    }
}
```

### 9. ErrorBypassDetector.java
```java
/**
 * 错误绕行检测器
 */
@Slf4j
@Service
public class ErrorBypassDetector extends RiskDetector<RiskErrorBypassRecordDO> {

    /**
     * 获取检测的风险类型
     *
     * @return
     */
    @Override
    public RiskCaseTypeEnum getDetectRiskType() {
        return RiskCaseTypeEnum.ERROR_BYPASS;
    }
}
```

## 总结

实现错误绕行检测记录功能需要新增9个关键文件，并修改1-2个枚举类文件。这些文件包括：
1. 持久化对象(PO)
2. 领域对象(DO)
3. Mapper接口
4. 转换器
5. 查询参数DTO
6. 仓储接口
7. 仓储实现
8. 工厂类
9. 检测器实现

修改的文件包括：
1. RiskCaseTypeEnum.java - 添加错误绕行类型

参考现有实现方式，严格遵循领域驱动设计的原则进行开发，可确保代码的一致性和可维护性。 