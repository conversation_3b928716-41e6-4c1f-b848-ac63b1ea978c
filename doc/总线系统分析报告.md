# 风险中心系统集成全面分析报告

## 1. 外部系统集成清单

### 1.1 数据总线系统

#### VehicleAdapter（EVE数据总线适配器）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/VehicleAdapter.java`
- **功能**：EVE数据总线的核心适配器，负责车辆实时信息查询
- **调用方式**：同步HTTP调用
- **接口地址**：`/eve/online/rest/data/bus/batch/get`

**主要方法**：
- `queryRuntimeVehicleInfo()` - 批量查询车辆实时信息
- `queryRuntimeVehicleInfoByVin()` - 单车辆实时信息查询
- `getVehicleHdMapVersion()` - 获取车辆高精地图版本
- `queryReserveVehicleByTimeAndPlace()` - 查询约车信息

#### VehicleStatusAdapter（车辆状态查询）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/VehicleStatusAdapter.java`
- **功能**：查询车辆实时状态信息
- **调用方式**：同步HTTP GET请求

### 1.2 通知和消息系统

#### DxNoticeAdapter（大象通知服务）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/DxNoticeAdapter.java`
- **功能**：DingTalk消息和卡片发送
- **调用方式**：同步Thrift调用

**主要功能**：
- `createOrUpdateDxMessage()` - 创建或更新大象消息
- `sendDxCard()` - 发送DingTalk卡片
- `createDxGroup()` - 创建大象群聊
- `updateDxGroupRoleName()` - 更新群聊角色

#### AutoCallAdapter（自动外呼服务）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/AutoCallAdapter.java`
- **功能**：自动外呼功能
- **调用方式**：同步Thrift调用

### 1.3 工单和坐席系统

#### WorkstationAdapter（工单系统适配器）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/WorkstationAdapter.java`
- **功能**：工单系统集成
- **调用方式**：同步HTTP调用（BA认证）

**主要功能**：
- `queryWorkstationCaseIdList()` - 查询工单列表
- `createWorkstationCase()` - 创建工单

#### ReTicketAdapter（RE工单适配器）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/ReTicketAdapter.java`
- **功能**：RE工单查询
- **调用方式**：同步HTTP调用

#### CloudCursorAdapter（云控适配器）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/CloudCursorAdapter.java`
- **功能**：云控系统集成
- **调用方式**：同步HTTP调用（BA认证）

### 1.4 地图和位置服务

#### GisAdapter（地理信息服务）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/GisAdapter.java`
- **功能**：地理位置查询和逆地理编码
- **调用方式**：同步HTTP调用 + Thrift调用

**主要功能**：
- `getGisInfo()` - 获取地理信息
- `getAdressByIp()` - IP定位服务

#### HdMapAdapter（高精地图适配器）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/HdMapAdapter.java`
- **功能**：高精地图数据查询
- **调用方式**：同步调用
- **缓存机制**：5小时刷新的本地缓存

#### PlaceAdapter（场地服务）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/PlaceAdapter.java`
- **功能**：场地信息查询
- **调用方式**：同步Thrift调用

### 1.5 用户和权限系统

#### UserInfoRepositoryImpl（用户信息服务）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/repository/adapterrepo/impl/UserInfoRepositoryImpl.java`
- **功能**：MIS到UID转换服务
- **调用方式**：同步Thrift调用

**主要方法**：
- `batchGetUidsByMis()` - 批量MIS转UID
- `getUids()` - 获取UID列表

#### TTRgOnCallAdapter（TT排班服务）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/TTRgOnCallAdapter.java`
- **功能**：TT排班查询
- **调用方式**：同步Thrift调用

### 1.6 搜索和查询系统

#### CommonSearchAdaptor（通用搜索服务）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/CommonSearchAdaptor.java`
- **功能**：Eagle通用搜索服务
- **调用方式**：同步Thrift调用

#### EventPlatSearchAdapter（事件平台搜索）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/EventPlatSearchAdapter.java`
- **功能**：事件平台数据查询
- **调用方式**：同步HTTP调用

### 1.7 知识管理和文档系统

#### KmServiceAdapter（知识管理服务）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/KmServiceAdapter.java`
- **功能**：知识管理系统集成
- **调用方式**：同步Thrift调用

#### XmServiceAdapter（XM服务适配器）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/XmServiceAdapter.java`
- **功能**：XM开放平台认证
- **调用方式**：同步Thrift调用

### 1.8 数据平台系统

#### DataPlatformAdapter（数据平台适配器）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/DataPlatformAdapter.java`
- **功能**：数据平台快速回传
- **调用方式**：同步HTTP调用（BA认证）

## 2. 缓存系统

### 2.1 Redis缓存（SquirrelAdapter）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/adaptar/client/SquirrelAdapter.java`
- **功能**：Redis分布式缓存管理
- **支持操作**：String、Hash、Set、List等数据结构

**主要功能**：
- `get/set` - 基础键值操作
- `hget/hset` - Hash操作
- `sadd/smembers` - Set操作
- `getStrAsObject/saveObjectAsStr` - 对象序列化缓存

### 2.2 本地缓存
- **HdMapAdapter**：地图元素缓存（5小时刷新）
- **VehicleAdapter**：HdMapVersion缓存（1小时）

## 3. 消息队列系统

### 3.1 消息生产者（CommonMessageProducer）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/producer/CommonMessageProducer.java`
- **功能**：统一消息发送服务
- **支持类型**：普通消息、延迟消息、自定义消息

### 3.2 消息消费者

#### DomainEventConsumer（领域事件消费者）
- **位置**：`wallemonitor-risk-center-server/src/main/java/com/sankuai/wallemonitor/risk/center/server/consumer/DomainEventConsumer.java`
- **消费主题**：
  - `wallemonitor.risk.center.domain.event` - 同步领域事件
  - `wallemonitor.risk.center.domain.event.async` - 异步领域事件
  - `risk.center.risk_checking_queue_item.domain.event` - 预检模型事件

#### AutoMarkConsumer（自动标注消费者）
- **位置**：`wallemonitor-risk-center-server/src/main/java/com/sankuai/wallemonitor/risk/center/server/consumer/AutoMarkConsumer.java`
- **消费主题**：`wallemonitor.risk.auto.mark.message`

#### FCDriveTaskRouteEventConsumer（FC驾驶任务路线事件消费者）
- **位置**：`wallemonitor-risk-center-server/src/main/java/com/sankuai/wallemonitor/risk/center/server/consumer/FCDriveTaskRouteEventConsumer.java`
- **功能**：处理FC驾驶任务路线事件

## 4. 数据库系统

### 4.1 Repository层实现

#### RiskCaseRepositoryImpl（风险事件仓储）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/repository/dbrepo/impl/RiskCaseRepositoryImpl.java`
- **功能**：风险事件数据持久化
- **主要方法**：
  - `queryByParam()` - 条件查询
  - `getByEventId()` - 根据事件ID查询
  - `getByCaseId()` - 根据案例ID查询

#### SafetyAreaRepositoryImpl（安全区域仓储）
- **位置**：`wallemonitor-risk-center-infra/src/main/java/com/sankuai/wallemonitor/risk/center/infra/repository/dbrepo/impl/SafetyAreaRepositoryImpl.java`
- **功能**：安全区域数据管理
- **缓存机制**：停车场区域缓存自动刷新

### 4.2 数据访问特性
- **分库分表**：支持Zebra分库分表
- **读写分离**：@ZebraForceMaster强制主库
- **事务管理**：@RepositoryExecute事务注解
- **查询优化**：@RepositoryQuery查询注解

## 5. 定时任务系统

### 5.1 车辆上下文更新任务
- **类名**：`VehicleRuntimeInfoContextUpdateFromDataBusCrane`
- **功能**：从数据总线获取车辆业务状态并更新缓存
- **执行频率**：定时执行
- **批处理**：支持并行处理，默认200个VIN一批

### 5.2 风险检查任务
- **CheckRiskCaseCallMrmCrane**：检查风险事件呼叫坐席
- **CheckRiskCaseLongTimeCallMrmCrane**：长时间风险事件检查
- **SecurityCallCloudControlCrane**：安全呼叫云控

### 5.3 工单相关任务
- **ReportMoveCarOrderCrane**：上报挪车工单
- **HistoryRiskCaseRelatedWorkstationCrane**：历史风险事件关联工单

## 6. 数据结构分析

### 6.1 EVE总线数据结构（EveBusData）

```java
public static class EveBusData {
    private String vin;                    // 车架号
    private MonitorCompute monitorCompute; // 监控计算数据
    private Monitor monitor;               // 监控数据
    private ReserveVehicle reserveVehicle; // 约车数据
    private LabelData label;               // 标签数据
    private VehicleManage vehicleManage;   // 车辆管理数据
    private MaintenanceOrder maintenanceOrder; // 维修工单
    private RescueOrder rescueOrder;       // 救援工单
    private AccidentCompute accidentCompute; // 事故计算
    private Wdp wdp;                      // 履约相关
    private ReOrder reOrder;              // Re工单
}
```

### 6.2 车辆信息VO（VehicleEveInfoVTO）

**核心字段**：
- `vin` - 车架号
- `vehicleId` - 车辆ID（如M1234）
- `vehicleName` - 车辆名称（如S20-123）
- `position` - 车辆位置（WGS84坐标系）
- `placeCode` - 场地代码
- `autocarVersion` - 自动驾驶版本
- `driveMode` - 驾驶模式
- `speed` - 行驶速度（km/h）
- `telecontrol` - 云控安全员
- `substitute` - 近场安全员
- `bizStatus` - 业务状态

### 6.3 约车数据结构（ReserveVehicle）

```java
public static class ReserveVehicle {
    private List<VresvData> vresvList; // 约车记录列表
}

public static class VresvData {
    private String resvId;      // 约车ID
    private String startTime;   // 开始时间
    private String endTime;     // 结束时间
    private String substitute;  // 近场安全员
    private String telecontrol; // 云控安全员
    private String locationName; // 位置名称
    private String approved;    // 审批状态
    private String deleted;     // 删除状态
}
```

## 7. 业务流程映射

### 7.1 核心业务流程

#### 风险事件处理流程
1. **事件接收** → MQ消费者接收风险事件
2. **车辆信息查询** → VehicleAdapter查询车辆实时信息
3. **安全员信息提取** → 从约车数据中获取安全员MIS
4. **UID转换** → UserInfoRepository将MIS转换为UID
5. **告警发送** → DxNoticeAdapter发送DingTalk卡片
6. **工单创建** → WorkstationAdapter创建处置工单
7. **坐席分配** → 根据策略分配坐席资源

#### 车辆上下文更新流程
1. **定时任务触发** → VehicleRuntimeInfoContextUpdateFromDataBusCrane
2. **批量查询** → 调用EVE数据总线获取车辆业务状态
3. **数据处理** → 提取和转换业务状态信息
4. **缓存更新** → SquirrelAdapter更新Redis缓存
5. **状态同步** → 同步到车辆运行时上下文

#### 告警升级流程
1. **升级条件检查** → 定时任务检查升级条件
2. **组长信息查询** → 通过总线查询组长信息
3. **MIS转UID** → 用户信息服务转换
4. **升级卡片发送** → DxNoticeAdapter发送升级告警
5. **外呼触发** → AutoCallAdapter触发自动外呼

### 7.2 系统间交互关系

#### 上游依赖系统
- **EVE数据总线**：车辆实时数据、约车信息
- **约车系统**：安全员分配信息
- **UDB服务**：用户身份信息
- **地图服务**：地理位置和高精地图数据
- **TT系统**：排班和值班信息
- **事件平台**：历史事件数据

#### 下游消费系统
- **DingTalk**：消息和卡片推送
- **工单系统**：工单创建和管理
- **坐席系统**：坐席资源调度
- **云控系统**：远程控制指令
- **数据平台**：数据回传和分析

### 7.3 数据流向分析

#### 实时数据流
```
EVE总线 → VehicleAdapter → 业务逻辑 → 缓存系统 → 前端展示
```

#### 告警数据流
```
风险事件 → 消息队列 → 业务处理 → 用户服务 → DingTalk推送
```

#### 工单数据流
```
风险判断 → 工单创建 → 工单系统 → 坐席分配 → 处置反馈
```

## 8. 技术架构特点

### 8.1 集成模式
- **适配器模式**：统一外部系统访问接口
- **门面模式**：简化复杂系统调用
- **策略模式**：支持多种处理策略
- **观察者模式**：事件驱动的消息处理

### 8.2 调用方式
- **同步调用**：HTTP REST、Thrift RPC
- **异步调用**：MQ消息队列
- **批量处理**：支持批量数据查询
- **并行处理**：ParallelExecutor并发执行

### 8.3 容错和性能
- **超时控制**：所有外部调用设置超时
- **重试机制**：关键调用支持重试
- **熔断保护**：防止级联故障
- **缓存策略**：多层缓存提升性能
- **批量优化**：减少网络调用次数

### 8.4 监控和运维
- **链路追踪**：MTrace分布式追踪
- **操作日志**：@OperateEnter操作记录
- **性能监控**：执行时间统计
- **异常告警**：异常情况自动告警

## 9. 系统总结

风险中心系统是一个高度集成的复杂系统，涉及20+个外部系统的集成，包括：

### 9.1 核心能力
- **实时数据获取**：通过EVE数据总线获取车辆实时状态
- **智能告警推送**：基于DingTalk的多层级告警机制
- **自动化处置**：工单自动创建和坐席智能分配
- **全链路追踪**：从事件发生到处置完成的全流程跟踪

### 9.2 技术亮点
- **高并发处理**：支持大规模车辆数据的实时处理
- **多系统集成**：统一的适配器层简化系统集成复杂度
- **智能缓存**：多层缓存策略提升系统响应速度
- **弹性架构**：具备良好的容错和恢复能力

### 9.3 业务价值
- **提升响应速度**：自动化流程大幅缩短事件处理时间
- **降低运营成本**：减少人工干预，提高处理效率
- **增强安全保障**：实时监控和快速响应机制
- **数据驱动决策**：丰富的数据分析支持业务优化

这个系统不仅仅是一个总线系统，而是一个完整的风险管理和应急响应平台，为自动驾驶业务提供了强有力的安全保障。
