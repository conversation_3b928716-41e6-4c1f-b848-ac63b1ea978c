<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <artifactId>wallemonitor-risk-center-api</artifactId>
  <dependencies>
    <dependency>
      <artifactId>walle-eve-domain</artifactId>
      <groupId>com.sankuai.walleeve</groupId>
      <version>1.1.6</version>
    </dependency>
    <dependency>
      <artifactId>lombok</artifactId>
      <groupId>org.projectlombok</groupId>
    </dependency>
    <dependency>
      <artifactId>swift-annotations</artifactId>
      <groupId>com.facebook.swift</groupId>
    </dependency>
    <dependency>
      <groupId>com.sankuai.walledelivery</groupId>
      <artifactId>walle-delivery-utils</artifactId>
      <version>1.0.11</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.sankuai</groupId>
        <artifactId>inf-bom</artifactId>
        <version>1.13.1</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <artifactId>walle-eve-common</artifactId>
        <groupId>com.sankuai.walleeve</groupId>
        <scope>import</scope>
        <type>pom</type>
        <version>${walle-eve-dependencies.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <distributionManagement>
    <repository>
      <id>meituan-nexus-releases</id>
      <name>Meituan Nexus Releases Repository</name>
      <url>http://maven.sankuai.com/nexus/content/repositories/releases/</url>
    </repository>

    <snapshotRepository>
      <id>meituan-nexus-snapshots</id>
      <name>Meituan Nexus Snapshots Repository</name>
      <url>http://maven.sankuai.com/nexus/content/repositories/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <walle-eve-dependencies.version>1.0.0</walle-eve-dependencies.version>
  </properties>


  <groupId>com.sankuai.wallemonitor</groupId>
  <modelVersion>4.0.0</modelVersion>
  <name>wallemonitor-risk-center-api</name>
  <packaging>jar</packaging>

  <version>1.0.5</version>

</project>
