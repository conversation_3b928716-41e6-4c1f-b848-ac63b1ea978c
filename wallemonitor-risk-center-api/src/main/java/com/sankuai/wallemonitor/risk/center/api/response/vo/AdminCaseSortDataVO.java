package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * case分拣数据VO
 */

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AdminCaseSortDataVO {

    @ThriftField(1)
    @FieldDoc(description = "事件Id", type = String.class, requiredness = Requiredness.REQUIRED)
    private String caseId;

    @ThriftField(2)
    @FieldDoc(description = "所属问题", type = String.class, requiredness = Requiredness.REQUIRED)
    private String problem;

    @ThriftField(3)
    @FieldDoc(description = "最近分拣人", type = String.class, requiredness = Requiredness.REQUIRED)
    private String sorter;

    @ThriftField(4)
    @FieldDoc(description = "创建时间", type = String.class, requiredness = Requiredness.REQUIRED)
    private String createTime;

    @ThriftField(5)
    @FieldDoc(description = "最近更新时间", type = String.class, requiredness = Requiredness.REQUIRED)
    private String updateTime;

    @ThriftField(6)
    @FieldDoc(description = "补充信息", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String extInfo;

    @ThriftField(7)
    @FieldDoc(description = "分拣描述", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String description;

}
