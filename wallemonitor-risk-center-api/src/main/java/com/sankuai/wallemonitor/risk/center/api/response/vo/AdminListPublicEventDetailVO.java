package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AdminListPublicEventDetailVO {

    @ThriftField(1)
    @FieldDoc(description = "舆情事件ID", rule = "唯一标识", example = "EVT123456", requiredness = Requiredness.REQUIRED)
    private Long publicEventId;

    @ThriftField(2)
    @FieldDoc(description = "车架号列表", rule = "非空", example = "[\"VIN12345\",\"VIN67890\"]", requiredness = Requiredness.REQUIRED)
    private String vins;

    @ThriftField(3)
    @FieldDoc(description = "舆情来源", rule = "非空", example = "网络", requiredness = Requiredness.REQUIRED)
    private String sourceType;

    @ThriftField(4)
    @FieldDoc(description = "实际发生时间", rule = "非空", example = "2023-04-01 12:00:00", requiredness = Requiredness.REQUIRED)
    private String occurTime;

    @ThriftField(5)
    @FieldDoc(description = "舆情类型", rule = "非空", example = "负面", requiredness = Requiredness.REQUIRED)
    private String type;

    @ThriftField(6)
    @FieldDoc(description = "舆情子类型", rule = "非空", example = "投诉", requiredness = Requiredness.REQUIRED)
    private String subType;

    @ThriftField(7)
    @FieldDoc(description = "责任方", rule = "非空", example = "供应商", requiredness = Requiredness.REQUIRED)
    private String responsibleParty;

    @ThriftField(8)
    @FieldDoc(description = "描述", rule = "非空", example = "详细描述事件情况", requiredness = Requiredness.REQUIRED)
    private String description;

    @ThriftField(9)
    @FieldDoc(description = "处置人", rule = "非空", example = "张三", requiredness = Requiredness.REQUIRED)
    private String handlers;

    @ThriftField(10)
    @FieldDoc(description = "创建时间", rule = "自动生成", example = "2023-04-01 12:00:00", requiredness = Requiredness.REQUIRED)
    private String createTime;

    @ThriftField(11)
    @FieldDoc(description = "最近更新时间", rule = "自动更新", example = "2023-04-02 12:00:00", requiredness = Requiredness.REQUIRED)
    private String updateTime;

    @ThriftField(12)
    @FieldDoc(description = "拓展信息", rule = "可选", example = "{\"key\":\"value\"}", requiredness = Requiredness.OPTIONAL)
    private String extInfo;

    @ThriftField(13)
    @FieldDoc(description = "模式", rule = "非空", example = "自动", requiredness = Requiredness.REQUIRED)
    private Integer driveMode;

    @ThriftField(14)
    @FieldDoc(description = "发现类型", rule = "非空", example = "系统", requiredness = Requiredness.REQUIRED)
    private String discoveryType;

    @ThriftField(15)
    @FieldDoc(description = "处理类型", rule = "非空", example = "1", requiredness = Requiredness.REQUIRED)
    private Integer handleType;

    @ThriftField(16)
    @FieldDoc(description = "请求帮助时间", rule = "非空", example = "2023-04-01 12:00:00", requiredness = Requiredness.REQUIRED)
    private String requestHelpTime;

    @ThriftField(17)
    @FieldDoc(description = "开始处理时间", rule = "非空", example = "2023-04-01 13:00:00", requiredness = Requiredness.REQUIRED)
    private String startHandleTime;

    @ThriftField(18)
    @FieldDoc(description = "完成时间", rule = "非空", example = "2023-04-01 14:00:00", requiredness = Requiredness.REQUIRED)
    private String finishTime;

    @ThriftField(19)
    @FieldDoc(description = "城市", rule = "非空", example = "北京", requiredness = Requiredness.REQUIRED)
    private String city;

    @ThriftField(20)
    @FieldDoc(description = "风险案例ID", rule = "唯一标识", example = "RC123456", requiredness = Requiredness.REQUIRED)
    private String riskCaseId;

    @ThriftField(21)
    @FieldDoc(description = "标题", rule = "非空", example = "事件标题", requiredness = Requiredness.REQUIRED)
    private String title;

    @ThriftField(22)
    @FieldDoc(description = "车辆全程", rule = "非空", example = "[\"M123123|LSMT123123\",\"M123123|LSMT123123\"]", requiredness = Requiredness.REQUIRED)
    private List<String> vehicleList;

    @ThriftField(23)
    @FieldDoc(description = "群组", rule = "群组id", example = "事件标题", requiredness = Requiredness.REQUIRED)
    private String groupId;

    @ThriftField(24)
    @FieldDoc(description = "车辆id号", rule = "非空", example = "[\"VIN12345\",\"VIN67890\"]", requiredness = Requiredness.REQUIRED)
    private String vehicleIds;

    @ThriftField(25)
    @FieldDoc(description = "类别", requiredness = Requiredness.REQUIRED)
    private String category;

}