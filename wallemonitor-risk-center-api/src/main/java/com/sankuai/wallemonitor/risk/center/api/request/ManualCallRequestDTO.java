package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "手动调用请求数据传输对象")
public class ManualCallRequestDTO {

    @ThriftField(1)
    @FieldDoc(description = "案例标识", rule = "必传", example = "case12345", requiredness = Requiredness.REQUIRED)
    private String caseId;

}
