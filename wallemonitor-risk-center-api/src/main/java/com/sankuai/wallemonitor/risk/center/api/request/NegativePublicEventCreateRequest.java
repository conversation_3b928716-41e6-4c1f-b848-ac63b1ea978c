package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NegativePublicEventCreateRequest {

    /**
     * 事件来源
     */
    @ThriftField(1)
    @FieldDoc(description = "事件来源")
    private List<Integer> sourceList;

    /**
     * 来源类型
     */
    @ThriftField(2)
    @FieldDoc(description = "来源类型")
    private Integer type;

    /**
     * 发生时间
     */
    @ThriftField(3)
    @FieldDoc(description = "发生时间")
    private String occurTime;

    /**
     * 感知时间
     */
    @ThriftField(4)
    @FieldDoc(description = "感知时间")
    private String perceiveTime;

    /**
     * 省
     */
    @ThriftField(5)
    @FieldDoc(description = "省")
    private String province;

    /**
     * 城市
     */
    @ThriftField(6)
    @FieldDoc(description = "城市")
    private String city;

    /**
     * 区
     */
    @ThriftField(7)
    @FieldDoc(description = "区")
    private String district;

    /**
     * 详细地址
     */
    @ThriftField(8)
    @FieldDoc(description = "详细地址")
    private String location;

    /**
     * 事件描述
     */
    @ThriftField(9)
    @FieldDoc(description = "事件描述")
    private String eventDesc;

    /**
     * 关联的文件链接
     */
    @ThriftField(10)
    @FieldDoc(description = "关联的文件链接")
    private List<String> relatedFileLinks;


}
