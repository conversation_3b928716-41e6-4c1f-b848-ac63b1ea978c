package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.VehicleRiskStatusQueryRequest;
import com.sankuai.wallemonitor.risk.center.api.response.vo.VehicleRiskStatusVO;
import java.util.List;

@InterfaceDoc(type = "type = octo.thrift.annotation", displayName = "车辆风险状态查询接口", scenarios = "车辆风险状态查询")
@ThriftService
public interface IThriftVehicleRiskStatusQueryService {

    @MethodDoc(
            displayName = "批量查询车辆风险状态",
            description = "批量查询车辆风险状态",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<List<VehicleRiskStatusVO>> batchQueryVehicleRiskStatusByVinList(
            VehicleRiskStatusQueryRequest request);
}
