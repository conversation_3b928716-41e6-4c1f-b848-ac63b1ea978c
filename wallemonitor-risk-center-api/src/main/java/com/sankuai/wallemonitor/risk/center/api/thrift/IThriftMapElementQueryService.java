package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.service.ThriftException;
import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walledelivery.thrift.exception.BizThriftException;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.LaneQueryRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.MapElementRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.vo.HdMapElementGeoVO;
import java.util.List;

@InterfaceDoc(type = "THRIFT", displayName = "车道查询服务", description = "提供基于距离的车道查询服务", scenarios = "用于自动驾驶场景中的车道信息获取")
@ThriftService
public interface IThriftMapElementQueryService {

    @MethodDoc(
            displayName = "根据距离搜索地图元素", description = "根据给定的位置和距离搜索附近的地图元素信息",
            parameters = {@ParamDoc(
                    name = "request", description = "车道查询请求参数",
                    example = "{\"distance\": 100.0, \"longitude\": 114.05, \"latitude\": 22.54}"
            )}, returnValueDescription = "返回符合条件的车道几何信息列表",
            responseParams = {@ParamDoc(name = "code", description = "响应码", example = {"0"}),
                @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                @ParamDoc(
                        name = "data", description = "车道几何信息列表",
                        example = "[{\"laneId\": \"lane_123\", \"points\": [[114.05, 22.54], [114.06, 22.55]]}]"
                )}
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 1)})
    EveThriftResponse<List<HdMapElementGeoVO>> searchMapElementByDistance(MapElementRequestDTO request);

    @MethodDoc(
            displayName = "根据ID搜索车道", description = "根据给定的车道ID搜索车道信息",
            parameters = {
                @ParamDoc(name = "request", description = "车道查询请求参数", example = "{\"laneId\": \"lane_123\"}")},
            returnValueDescription = "返回指定ID的车道几何信息",
            responseParams = {@ParamDoc(name = "code", description = "响应码", example = {"0"}),
                @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                @ParamDoc(
                        name = "data", description = "车道几何信息",
                        example = "{\"laneId\": \"lane_123\", \"points\": [[114.05, 22.54], [114.06, 22.55]]}"
                )}
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 1)})
    EveThriftResponse<List<HdMapElementGeoVO>> searchLaneById(LaneQueryRequestDTO request);
}
