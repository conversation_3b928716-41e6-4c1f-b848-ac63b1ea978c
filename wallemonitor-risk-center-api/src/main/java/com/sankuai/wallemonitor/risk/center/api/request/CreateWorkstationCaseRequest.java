package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CreateWorkstationCaseRequest {

    /**
     * 风险caseID
     */
    @ThriftField(1)
    @FieldDoc(description = "caseId ", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private String caseId;

}
