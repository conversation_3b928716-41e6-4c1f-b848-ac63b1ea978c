package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.service.ThriftException;
import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walledelivery.thrift.exception.BizThriftException;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftPageResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.AdminAddSafetyAreaRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminDeleteSafetyAreaRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminListActionChainResultRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminListRiskCaseRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminMarkRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminRiskLevelRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminUpdateCaseRequest;
import com.sankuai.wallemonitor.risk.center.api.request.CreateWorkstationCaseRequest;
import com.sankuai.wallemonitor.risk.center.api.request.ManualCallRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ManualHandleMrmCallRequest;
import com.sankuai.wallemonitor.risk.center.api.request.ManualRiskCaseOperateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.SearchAutoVehicleRequestVO;
import com.sankuai.wallemonitor.risk.center.api.response.AdminGetRiskCaseDetailResponse;
import com.sankuai.wallemonitor.risk.center.api.response.vo.ActionChainResultVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminListRiskCaseVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.CreateWorkstationCaseVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.SafetyAreaVO;
import com.sankuai.wallemonitor.risk.center.api.vo.VehicleRuntimeVO;
import java.util.List;

/**
 * 管理后台相关接口
 *
 * <AUTHOR>
 * @Date 2024/7/2
 */
@InterfaceDoc(type = "type = octo.thrift.annotation", displayName = "管理后台接口", description = "管理后台接口", scenarios = "后台管理")
@ThriftService
public interface IThriftAdminService {

    @MethodDoc(
            displayName = "人工标注case",
            description = "人工标注case",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = AdminMarkRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> mark(AdminMarkRequest request);


    @MethodDoc(
            displayName = "人工标注case",
            description = "人工标注case",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = AdminMarkRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<List<String>> batchMark(BatchAdminMarkRequest request);



    @MethodDoc(
            displayName = "手动操作解除风险事件",
            description = "手动操作解除风险事件",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> manualRiskCaseOperate(ManualRiskCaseOperateRequest request);

    @MethodDoc(
            displayName = "手动操作修改风险等级",
            description = "手动操作修改风险等级",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> changeRiskLevel(AdminRiskLevelRequest request);

    @MethodDoc(
            displayName = "查询case详细信息",
            description = "查询case详细信息",
            parameters = {
                    @ParamDoc(name = "caseId", description = "caseId", type = String.class, example = "9b544f67ca544729b955cfe010ebe1d8")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "case详细数据", type = AdminGetRiskCaseDetailResponse.class, example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<AdminGetRiskCaseDetailResponse> getRiskCaseDetail(String caseId);

    @MethodDoc(
            displayName = "查询Case执行链结果",
            description = "查询Case执行链结果",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = AdminListRiskCaseRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "执行链结果列表", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<List<ActionChainResultVO>> listActionChainResult(AdminListActionChainResultRequest request);

    @MethodDoc(
            displayName = "查询case列表",
            description = "查询case列表",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = AdminListRiskCaseRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "case列表", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftPageResponse<List<AdminListRiskCaseVO>> listRiskCase(AdminListRiskCaseRequest request);

    @MethodDoc(
            displayName = "手动调用MRM",
            description = "手动调用MRM接口",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = ManualCallRequestDTO.class, example = "{}")
            },
            returnValueDescription = "分页响应",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "空响应数据", type = EmptyResponse.class, example = "{}")
            }
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 1)})
    EveThriftResponse<EmptyResponse> manualCallMrm(ManualCallRequestDTO request);

    @MethodDoc(
            displayName = "查询全量区域适配信息",
            description = "查询全量区域适配信息",
            parameters = {
            },
            returnValueDescription = "分页响应",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "空响应数据", type = List.class, example = "{}")
            }
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 1)})
    EveThriftResponse<List<SafetyAreaVO>> querySafetyAreaList();

    @MethodDoc(
            displayName = "创建自动车平台case",
            description = "创建自动车平台case",
            parameters = {
            },
            returnValueDescription = "分页响应",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "响应创建工作台case的链接", type = CreateWorkstationCaseVO.class, example = "{}")
            }
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 1)})
    EveThriftResponse<CreateWorkstationCaseVO> createWorkstationCase(CreateWorkstationCaseRequest request);

    // 增加版本查询
    @MethodDoc(
            displayName = "查询版本信息", description = "查询版本信息", parameters = {}, returnValueDescription = "分页响应",
            responseParams = {
                    //
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "响应版本信息", type = List.class, example = "{}")}
    )
    @ThriftMethod
    EveThriftResponse<List<String>> queryMarkVersionList();

    // 手动发起/取消云控呼叫
    @MethodDoc(
            displayName = "人工发起/取消云控呼叫", description = "人工发起/取消云控呼叫", parameters = {}, returnValueDescription = "",
            responseParams = {
                    //
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "响应版本信息", type = List.class, example = "{}")}
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> manualHandleMrmCall(ManualHandleMrmCallRequest request);

    // 添加接口：添加安全区域

    // 添加接口：添加安全区域
    @MethodDoc(
            displayName = "添加安全区域", description = "添加安全区域",
            parameters = {@ParamDoc(
                    name = "request", description = "请求参数(包含多边形点坐标)", type = AdminAddSafetyAreaRequest.class,
                    example = "{}"
            )}, returnValueDescription = "添加结果",
            responseParams = {@ParamDoc(name = "code", description = "响应码", example = {"0"}),
                @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                @ParamDoc(name = "data", description = "空响应数据", type = EmptyResponse.class, example = "{}")}
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 1)})
    EveThriftResponse<String> addSafetyArea(AdminAddSafetyAreaRequest request);

    // 删除安全区
    @MethodDoc(
            displayName = "删除安全区域", description = "添加安全区域",
            parameters = {@ParamDoc(
                    name = "request", description = "请求参数", type = AdminAddSafetyAreaRequest.class, example = "{}"
            )}, returnValueDescription = "添加结果",
            responseParams = {@ParamDoc(name = "code", description = "响应码", example = {"0"}),
                @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                @ParamDoc(name = "data", description = "空响应数据", type = EmptyResponse.class, example = "{}")}
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 1)})
    EveThriftResponse<EmptyResponse> deleteSafetyArea(AdminDeleteSafetyAreaRequest request);

    //更新 case的发生开始时间，入参是 request类，有 occurTime字段
    @MethodDoc(
            displayName = "更新 case时间", description = "更新 case时间",
            parameters = {@ParamDoc(
                    name = "request", description = "请求参数", type = AdminUpdateCaseRequest.class, example = "{}"
            )}, returnValueDescription = "添加结果",
            responseParams = {@ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "空响应数据", type = EmptyResponse.class, example = "{}")}
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> updateCase(AdminUpdateCaseRequest request);


    // 根据定位和范围，查询车辆
    @MethodDoc(
            displayName = "查询车辆定位", description = "",
            parameters = {@ParamDoc(
                    name = "request", description = "请求参数", type = SearchAutoVehicleRequestVO.class, example = "{}"
            )}, returnValueDescription = "添加结果",
            responseParams = {@ParamDoc(name = "code", description = "响应码", example = {"0"}),
                @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                @ParamDoc(name = "data", description = "空响应数据", type = List.class, example = "{}")}
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 1)})
    EveThriftResponse<List<VehicleRuntimeVO>> searchAutoVehicleList(SearchAutoVehicleRequestVO request);
}
