package com.sankuai.wallemonitor.risk.center.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminCaseSortDataVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.RiskCaseBaseInfoVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.RiskCaseDisposeInfoVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.RiskCaseMarkInfoVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.RiskCaseVehicleVO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @<PERSON> kong<PERSON>an
 * @Date 2024/7/2
 */
@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AdminGetRiskCaseDetailResponse {

    @ThriftField(1)
    private RiskCaseBaseInfoVO base;

    @ThriftField(2)
    private List<RiskCaseVehicleVO> vehicleList;

    @ThriftField(3)
    private RiskCaseMarkInfoVO markInfo;

    @ThriftField(4)
    private List<RiskCaseDisposeInfoVO> disposeInfoList;

    @ThriftField(5)
    @FieldDoc(description = "分拣数据", type = AdminCaseSortDataVO.class, requiredness = Requiredness.OPTIONAL)
    private AdminCaseSortDataVO sortData;

    @ThriftField(6)
    @FieldDoc(description = "工作台接管caseID列表", type = List.class, requiredness = Requiredness.REQUIRED)
    private List<String> interventionCaseIdList;
}
