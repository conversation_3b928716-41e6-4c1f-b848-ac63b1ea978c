package com.sankuai.wallemonitor.risk.center.api.response.vo;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ActionChainResultVO {

    @ThriftField(1)
    @FieldDoc(description = "caseId", type = String.class, requiredness = Requiredness.REQUIRED)
    private String caseId;

    @ThriftField(2)
    @FieldDoc(description = "执行场景", type = String.class, requiredness = Requiredness.REQUIRED)
    private String scene;

    @ThriftField(3)
    @FieldDoc(description = "执行轮次", type = Integer.class, requiredness = Requiredness.REQUIRED)
    private Integer round;

    @ThriftField(4)
    @FieldDoc(description = "执行(开始)时间", type = String.class, requiredness = Requiredness.REQUIRED)
    private String checkTime;

    @ThriftField(5)
    @FieldDoc(description = "检出场景", type = String.class, requiredness = Requiredness.REQUIRED)
    private String category;

    @ThriftField(6)
    @FieldDoc(description = "检出action名称", type = String.class, requiredness = Requiredness.REQUIRED)
    private String actionName;

    @ThriftField(7)
    @FieldDoc(description = "检出结果详情", type = String.class, requiredness = Requiredness.REQUIRED)
    private String resultDetail;

    @ThriftField(8)
    @FieldDoc(description = "其余action执行结果", type = String.class, requiredness = Requiredness.REQUIRED)
    private String otherActionCheckDetails;

    @ThriftField(9)
    @FieldDoc(description = "执行耗时", type = Long.class, requiredness = Requiredness.REQUIRED)
    private Long duration;

    @ThriftField(10)
    @FieldDoc(description = "受检项快照", type = String.class, requiredness = Requiredness.REQUIRED)
    private String itemDataSnapshot;

    @ThriftField(11)
    @FieldDoc(description = "车架号", type = String.class, requiredness = Requiredness.REQUIRED)
    private String vin;

    @ThriftField(12)
    @FieldDoc(description = "车辆运行时上下文快照", type = String.class, requiredness = Requiredness.REQUIRED)
    private String vehicleRuntimeInfoSnapshot;

    @ThriftField(13)
    @FieldDoc(description = "额外数据", type = String.class, requiredness = Requiredness.REQUIRED)
    private String extInfo;

    @ThriftField(14)
    @FieldDoc(description = "创建时间", type = String.class, requiredness = Requiredness.REQUIRED)
    private String createTime;

    @ThriftField(15)
    @FieldDoc(description = "更新时间", type = String.class, requiredness = Requiredness.REQUIRED)
    private String updateTime;
}
