package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;

@InterfaceDoc(type = "type = octo.thrift.annotation", displayName = "微信小程序用户鉴权接口", description = "微信小程序用户鉴权接口", scenarios = "微信小程序用户鉴权")
@ThriftService
public interface IThriftUserAuthenticationService {

    @MethodDoc(
            displayName = "获取登陆态",
            description = "获取登陆态",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<String> getToken(String code);

}