package com.sankuai.wallemonitor.risk.center.api.request;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 分拣请求类
 */
@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AdminSortRequest {

    @ThriftField(1)
    @FieldDoc(description = "caseId ", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private String caseId;

    @ThriftField(2)
    @FieldDoc(description = "所属问题", example = "道路原因", requiredness = Requiredness.OPTIONAL)
    private String problem;

    @ThriftField(3)
    @FieldDoc(description = "描述", requiredness = Requiredness.OPTIONAL)
    private String description;

}
