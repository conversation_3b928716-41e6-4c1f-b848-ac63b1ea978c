package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.MoveCarEventQueryRequest;
import com.sankuai.wallemonitor.risk.center.api.request.MoveCarEventReportRequest;
import com.sankuai.wallemonitor.risk.center.api.vo.MoveCarEventStatusVO;

@InterfaceDoc(type = "type = octo.thrift.annotation", displayName = "扫码挪车事件管理接口", description = "扫码挪车事件管理接口", scenarios = "扫码挪车事件管理接口")
@ThriftService
public interface IThriftMoveCarEventAdminService {

    @MethodDoc(
            displayName = "扫码挪车事件上报",
            description = "扫码挪车事件上报",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> reportMoveCarEvent(MoveCarEventReportRequest request);

    @MethodDoc(
            displayName = "查询扫码挪车事件",
            description = "查询扫码挪车事件",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<MoveCarEventStatusVO> queryMoveCarEvent(MoveCarEventQueryRequest request);
}