package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TypeDoc(description = "挪车事件上报请求")
public class MoveCarEventQueryRequest extends WechatBasicFrontRequest {

    @ThriftField(1)
    @FieldDoc(description = "车牌号", rule = "必传", example = "张三", requiredness = Requiredness.REQUIRED)
    private String vehicleId;
}
