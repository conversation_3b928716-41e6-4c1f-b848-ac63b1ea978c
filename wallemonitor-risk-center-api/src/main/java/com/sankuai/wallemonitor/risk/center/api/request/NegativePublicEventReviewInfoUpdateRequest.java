package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NegativePublicEventReviewInfoUpdateRequest {

    /**
     * 事件ID
     */
    @ThriftField(1)
    @FieldDoc(description = "事件ID")
    private String eventId;

    /**
     * 复盘信息
     */
    @ThriftField(2)
    @FieldDoc(description = "复盘信息")
    private String reviewInfo;


}
