package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NegativePublicEventDetermineNatureInfoUpdateRequest {

    /**
     * 事件ID
     */
    @ThriftField(1)
    @FieldDoc(description = "事件ID")
    private String eventId;

    /**
     * 事件性质
     */
    @ThriftField(2)
    @FieldDoc(description = "事件性质")
    private Integer nature;

    /**
     * 问题分类
     */
    @ThriftField(3)
    @FieldDoc(description = "问题分类")
    private String category;

    /**
     * 问题分类-其他-描述
     */
    @ThriftField(4)
    @FieldDoc(description = "问题分类-其他-描述")
    private String otherCategoryDesc;

    /**
     * 事件等级
     */
    @ThriftField(5)
    @FieldDoc(description = "事件等级")
    private Integer level;

    /**
     * 标题
     */
    @ThriftField(6)
    @FieldDoc(description = "标题")
    private String title;

    /**
     * 情况说明
     */
    @ThriftField(7)
    @FieldDoc(description = "情况说明")
    private String conditionDesc;

    /**
     * 情况说明关联附件
     */
    @ThriftField(8)
    @FieldDoc(description = "情况说明关联附件")
    private List<String> conditionDescRelatedFileLinkList;

    /**
     * 处置人
     */
    @ThriftField(9)
    @FieldDoc(description = "处置人")
    private List<String> handlers;

    /**
     * 网络昵称
     */
    @ThriftField(10)
    @FieldDoc(description = "网络昵称")
    private String internetNickname;

    /**
     * 联系方式
     */
    @ThriftField(11)
    @FieldDoc(description = "联系方式")
    private String contactInfo;

    /**
     * 关联的车架号列表
     */
    @ThriftField(12)
    @FieldDoc(description = "关联的车架号列表")
    private List<String> relatedVins;

    /**
     * 事故链接
     */
    @ThriftField(13)
    @FieldDoc(description = "事故链接")
    private String accidentOrderLink;


}
