package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.AdminSortRequest;
import java.util.List;

/**
 * 管理后台-分拣结果相关接口
 *
 * <AUTHOR>
 * @Date 2024/7/2
 */
@ThriftService
@InterfaceDoc(type = "type = octo.thrift.annotation", displayName = "分拣结果接口", description = "分拣结果接口", scenarios = "后台管理")
public interface IThriftAdminSortService {

    @MethodDoc(
            displayName = "拉取分拣所属问题列表",
            description = "拉取分拣所属问题列表",
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<List<String>> listProblemList();

    @MethodDoc(
            displayName = "更新分拣结果",
            description = "更新分拣结果",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = AdminSortRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> sort(AdminSortRequest request);

}
