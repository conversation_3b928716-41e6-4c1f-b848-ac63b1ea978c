package com.sankuai.wallemonitor.risk.center.api.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "高精地图元素几何信息")
public class HdMapElementGeoVO {

    @ThriftField(1)
    @FieldDoc(description = "车道ID", example = "lane_123456", rule = "必传", requiredness = Requiredness.REQUIRED)
    private String laneId;

    @ThriftField(2)
    @FieldDoc(
            description = "车道坐标点列表", example = "[[114.05, 22.54], [114.06, 22.55]]", rule = "必传",
            requiredness = Requiredness.REQUIRED
    )
    private List<List<Double>> points;

    @ThriftField(3)
    @FieldDoc(
            description = "车道坐标点列表", example = "[[114.05, 22.54], [114.06, 22.55]]", rule = "必传",
            requiredness = Requiredness.REQUIRED
    )
    private List<List<Double>> laneMiddleLinePoints;

    @ThriftField(4)
    @FieldDoc(description = "车道类型", example = "1", rule = "必传", requiredness = Requiredness.REQUIRED)
    private String laneType;

    @ThriftField(5)
    @FieldDoc(description = "集合元素类型", example = "LINESTRING", rule = "必传", requiredness = Requiredness.REQUIRED)
    private String elementType;

    @ThriftField(6)
    @FieldDoc(description = "属性,json格式", example = "{}")
    private String properties;

}
