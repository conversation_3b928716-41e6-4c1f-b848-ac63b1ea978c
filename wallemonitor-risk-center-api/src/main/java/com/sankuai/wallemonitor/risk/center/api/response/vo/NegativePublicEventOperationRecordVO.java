package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NegativePublicEventOperationRecordVO {

    @ThriftField(1)
    @FieldDoc(description = "状态", type = Integer.class, requiredness = Requiredness.REQUIRED)
    private Integer status;

    @ThriftField(2)
    @FieldDoc(description = "状态描述", type = String.class, requiredness = Requiredness.REQUIRED)
    private String statusDes;

    @ThriftField(3)
    @FieldDoc(description = "操作人", type = String.class, requiredness = Requiredness.REQUIRED)
    private String operator;

    @ThriftField(4)
    @FieldDoc(description = "操作时间", type = String.class, requiredness = Requiredness.REQUIRED)
    private String operationTime;

}
