package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NegativePublicEventDetermineReasonInfoUpdateRequest {

    /**
     * 事件ID
     */
    @ThriftField(1)
    @FieldDoc(description = "事件ID")
    private String eventId;

    /**
     * 问题归因
     */
    @ThriftField(2)
    @FieldDoc(description = "问题归因")
    private List<String> reasonList;

    /**
     * 问题归因-其他-描述
     */
    @ThriftField(3)
    @FieldDoc(description = "问题归因-其他-描述")
    private String otherReasonDesc;

    /**
     * 排查结果
     */
    @ThriftField(4)
    @FieldDoc(description = "排查结果")
    private String checkResult;


}
