package com.sankuai.wallemonitor.risk.center.api.request;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实验结果分场景数据
 *
 * <AUTHOR>
 * @date 2024/11/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@TypeDoc(description = "实验结果分场景数据")
public class ExperimentalResultSceneDataRequestDTO {

    // 风险场景
    @ThriftField(1)
    @FieldDoc(description = "风险场景", rule = "ISCheckCategoryEnum.getSubcategory()", example = "", requiredness = Requiredness.REQUIRED)
    private String categoryName;

    // 实际事件数
    @ThriftField(2)
    @FieldDoc(description = "实际事件数", example = "", requiredness = Requiredness.REQUIRED)
    private Integer actualCaseCount;

    // 召回事件数
    @ThriftField(3)
    @FieldDoc(description = "召回事件数", example = "", requiredness = Requiredness.REQUIRED)
    private Integer recallCaseCount;

    // 漏召事件列表
    @ThriftField(4)
    @FieldDoc(description = "漏召事件列表", example = "", requiredness = Requiredness.OPTIONAL)
    private List<MissCaseAndTimes> missedEvents;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MissCaseAndTimes {

        // 事件ID
        @ThriftField(1)
        @FieldDoc(description = "事件ID", example = "", requiredness = Requiredness.REQUIRED)
        private String caseId;

        // 事件漏召次数
        @ThriftField(2)
        @FieldDoc(description = "事件漏召次数", example = "", requiredness = Requiredness.REQUIRED)
        private Integer times;
    }
}


