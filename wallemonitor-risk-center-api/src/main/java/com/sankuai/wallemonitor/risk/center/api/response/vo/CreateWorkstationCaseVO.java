package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CreateWorkstationCaseVO {

    @ThriftField(1)
    @FieldDoc(description = "工作台接管caseID列表", type = List.class, requiredness = Requiredness.OPTIONAL)
    private List<String> interventionCaseIdList;

}
