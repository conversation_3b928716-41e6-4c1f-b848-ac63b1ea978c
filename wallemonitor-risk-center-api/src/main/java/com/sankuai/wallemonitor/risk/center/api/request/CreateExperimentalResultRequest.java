package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@TypeDoc(description = "创建实验结果文档请求")
public class CreateExperimentalResultRequest {

    @ThriftField(1)
    @FieldDoc(description = "创建人empId", rule = "查询empId：https://qabetter.it.test.sankuai.com/org/emp/sync", example = "", requiredness = Requiredness.REQUIRED)
    private Integer operatorEmpId;

    @ThriftField(2)
    @FieldDoc(description = "标题", example = "", requiredness = Requiredness.REQUIRED)
    private String title;

    @ThriftField(3)
    @FieldDoc(description = "实验数据概览表格数据", example = "", requiredness = Requiredness.REQUIRED)
    private ExperimentalResultOverviewRequestDTO overview;

    @ThriftField(4)
    @FieldDoc(description = "分场景数据表格数据", example = "", requiredness = Requiredness.REQUIRED)
    private List<ExperimentalResultSceneDataRequestDTO> sceneDataList;

    @ThriftField(5)
    @FieldDoc(description = "漏召事件列表详情表格数据", example = "", requiredness = Requiredness.REQUIRED)
    private List<ExperimentalResultMissCaseDetailRequestDTO> missCaseDetailList;
}
