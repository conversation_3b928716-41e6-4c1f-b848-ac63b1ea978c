package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.walleeve.domain.dto.PositionDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AdminListRiskCaseVO {

    @ThriftField(1)
    @FieldDoc(description = "事件Id", type = String.class, requiredness = Requiredness.REQUIRED)
    private String caseId;

    @ThriftField(2)
    @FieldDoc(description = "事件类型", type = Integer.class, requiredness = Requiredness.REQUIRED)
    private Integer type;

    @ThriftField(3)
    @FieldDoc(description = "事件场地", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String placeCode;

    @ThriftField(4)
    @FieldDoc(description = "状态", type = Integer.class, requiredness = Requiredness.REQUIRED)
    private Integer status;

    @ThriftField(5)
    @FieldDoc(description = "事件来源", type = Integer.class, requiredness = Requiredness.REQUIRED)
    private Integer source;

    @ThriftField(6)
    @FieldDoc(description = "发生时间", type = String.class, requiredness = Requiredness.REQUIRED)
    private String occurTime;

    @ThriftField(7)
    @FieldDoc(description = "解除时间", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String closeTime;

    @ThriftField(8)
    @FieldDoc(description = "事发位置", type = PositionDTO.class, requiredness = Requiredness.OPTIONAL)
    private PositionDTO position;

    @ThriftField(9)
    @FieldDoc(description = "补充信息", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String extInfo;

    @ThriftField(10)
    @FieldDoc(description = "关联车辆列表", type = List.class, requiredness = Requiredness.OPTIONAL)
    private List<RiskCaseVehicleVO> vehicleList;

    @ThriftField(11)
    @FieldDoc(description = "标注数据", type = RiskCaseMarkInfoVO.class, requiredness = Requiredness.OPTIONAL)
    private RiskCaseMarkInfoVO markInfo;

    @ThriftField(12)
    @FieldDoc(description = "是否呼叫坐席", type = String.class, requiredness = Requiredness.OPTIONAL)
    private Boolean mrmCalled;

    @ThriftField(13)
    @FieldDoc(description = "呼叫坐席状态", type = Integer.class, requiredness = Requiredness.OPTIONAL)
    private Integer mrmCalledStatus;

    @ThriftField(14)
    @FieldDoc(description = "分拣数据", type = AdminCaseSortDataVO.class, requiredness = Requiredness.OPTIONAL)
    private AdminCaseSortDataVO sortData;

    @ThriftField(15)
    @FieldDoc(description = "呼叫云安全状态", type = Integer.class, requiredness = Requiredness.OPTIONAL)
    private Integer callSafety;

    @ThriftField(16)
    @FieldDoc(description = "工作台接管caseID列表", type = List.class, requiredness = Requiredness.OPTIONAL)
    private List<String> interventionCaseIdList;

    @ThriftField(17)
    @FieldDoc(description = "快速回传数据任务ID", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String fastUploadId;

    @ThriftField(18)
    @FieldDoc(description = "召回时间", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String recallTime;

    @ThriftField(19)
    @FieldDoc(description = "事件ID", type = String.class, requiredness = Requiredness.REQUIRED)
    private String eventId;

    @ThriftField(20)
    @FieldDoc(description = "事发地点", type = String.class, requiredness = Requiredness.REQUIRED)
    private String poiName;

}
