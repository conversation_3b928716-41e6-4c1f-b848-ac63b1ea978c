package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class SafetyAreaVO {


    @ThriftField(1)
    @FieldDoc(description = "区域ID", type = String.class, requiredness = Requiredness.REQUIRED)
    private String areaId;


    @ThriftField(2)
    @FieldDoc(description = "数据来源", type = String.class, requiredness = Requiredness.REQUIRED)
    private Integer source;


    @ThriftField(3)
    @FieldDoc(description = "区域范围，JSON", type = String.class, requiredness = Requiredness.REQUIRED)
    private String polygon;


    @ThriftField(4)
    @FieldDoc(description = "描述", type = String.class, requiredness = Requiredness.REQUIRED)
    private String description;


    @ThriftField(5)
    @FieldDoc(description = "其它信息", type = String.class, requiredness = Requiredness.REQUIRED)
    private String extInfo;

    @ThriftField(6)
    @FieldDoc(description = "类型", type = String.class, requiredness = Requiredness.REQUIRED)
    private String type;

}
