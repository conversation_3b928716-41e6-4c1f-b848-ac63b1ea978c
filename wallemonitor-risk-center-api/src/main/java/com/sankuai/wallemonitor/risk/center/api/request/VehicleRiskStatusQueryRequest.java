package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@Data
@ToString
public class VehicleRiskStatusQueryRequest {

    /**
     * 车架号列表
     */
    @ThriftField(1)
    @FieldDoc(description = "车架号列表")
    private List<String> vinList;

}
