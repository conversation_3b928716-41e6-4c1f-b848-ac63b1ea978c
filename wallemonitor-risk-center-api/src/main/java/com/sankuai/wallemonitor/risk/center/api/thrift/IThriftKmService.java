package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.CreateExperimentalResultRequest;

/**
 * 学城相关接口
 *
 * <AUTHOR>
 * @date 2024/11/21
 */
@InterfaceDoc(type = "type = octo.thrift.annotation", displayName = "学城相关接口", description = "学城相关接口", scenarios = "学城")
@ThriftService
public interface IThriftKmService {

    @MethodDoc(
            displayName = "创建实验结果文档",
            description = "创建实验结果文档",
            returnValueDescription = "创建是否成功",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<String> createExperimentalResultContent(CreateExperimentalResultRequest request);

}
