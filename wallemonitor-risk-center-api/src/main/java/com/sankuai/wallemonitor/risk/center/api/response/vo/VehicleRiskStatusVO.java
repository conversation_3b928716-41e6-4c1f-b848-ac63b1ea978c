package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class VehicleRiskStatusVO {

    @ThriftField(1)
    @FieldDoc(description = "车架号", type = String.class, requiredness = Requiredness.REQUIRED)
    private String vin;

    @ThriftField(2)
    @FieldDoc(description = "是否停滞不当", type = Boolean.class, requiredness = Requiredness.REQUIRED)
    private Boolean isImproperStranding;

}
