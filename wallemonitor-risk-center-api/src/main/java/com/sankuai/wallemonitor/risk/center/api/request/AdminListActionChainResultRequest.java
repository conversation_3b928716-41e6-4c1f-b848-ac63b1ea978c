package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AdminListActionChainResultRequest {

    @ThriftField(1)
    @FieldDoc(description = "caseId", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private String caseId;

    @ThriftField(2)
    @FieldDoc(description = "运行场景", rule = "必传", example = "", requiredness = Requiredness.OPTIONAL)
    private String scene;

    @ThriftField(3)
    @FieldDoc(description = "执行轮次", rule = "必传", example = "", requiredness = Requiredness.OPTIONAL)
    private Integer round;

    // version
    @ThriftField(4)
    @FieldDoc(description = "版本号", rule = "必传", example = "", requiredness = Requiredness.OPTIONAL)
    private String version;

}
