package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实验结果概览数据
 *
 * <AUTHOR>
 * @date 2024/11/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@TypeDoc(description = "实验结果概览数据")
public class ExperimentalResultOverviewRequestDTO {

    @ThriftField(1)
    @FieldDoc(description = "实验时间", example = "1732168656105", requiredness = Requiredness.REQUIRED)
    private long experimentalTime;

    @ThriftField(2)
    @FieldDoc(description = "实验人名", example = "", requiredness = Requiredness.REQUIRED)
    private String personName;

    @ThriftField(3)
    @FieldDoc(description = "实验人mis", example = "", requiredness = Requiredness.REQUIRED)
    private String personMis;

    @ThriftField(4)
    @FieldDoc(description = "实验内容", example = "", requiredness = Requiredness.REQUIRED)
    private List<String> experimentalContent;

    @ThriftField(5)
    @FieldDoc(description = "数据集名称", example = "", requiredness = Requiredness.REQUIRED)
    private String dataset;

    @ThriftField(6)
    @FieldDoc(description = "数据集数据个数", example = "", requiredness = Requiredness.REQUIRED)
    private Integer datasetDataCount;

    @ThriftField(7)
    @FieldDoc(description = "实验轮次", example = "", requiredness = Requiredness.REQUIRED)
    private Integer round;

    @ThriftField(8)
    @FieldDoc(description = "召回数", example = "", requiredness = Requiredness.REQUIRED)
    private Integer recallCount;

    @ThriftField(9)
    @FieldDoc(description = "漏召数", example = "", requiredness = Requiredness.REQUIRED)
    private Integer missCount;

    @ThriftField(10)
    @FieldDoc(description = "失败数", example = "", requiredness = Requiredness.REQUIRED)
    private Integer failureCount;

    @ThriftField(11)
    @FieldDoc(description = "准确数", example = "", requiredness = Requiredness.REQUIRED)
    private Integer accuracyCount;

}
