package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "公共事件管理保存请求")
public class PublicEventAdminSaveRequest {


    @ThriftField(1)
    @FieldDoc(description = "车辆VIN码列表", example = "[\"VIN1234\", \"VIN5678\"]", rule = "非空列表", requiredness = Requiredness.REQUIRED)
    private String vins;

    @ThriftField(2)
    @FieldDoc(description = "车辆ID列表", example = "[\"ID1234\", \"ID5678\"]", rule = "非空列表", requiredness = Requiredness.REQUIRED)
    private String vehicleIds;

    @ThriftField(3)
    @FieldDoc(description = "事件发生时间", example = "1625140800000", rule = "时间戳格式", requiredness = Requiredness.REQUIRED)
    private String occurTime;

    @ThriftField(4)
    @FieldDoc(description = "事件发生城市", example = "北京", rule = "非空字符串", requiredness = Requiredness.REQUIRED)
    private String city;

    @ThriftField(5)
    @FieldDoc(description = "风险案例ID", example = "RISK123", rule = "非空字符串", requiredness = Requiredness.REQUIRED)
    private String riskCaseId;

    @ThriftField(6)
    @FieldDoc(description = "事件标题", example = "重大风险事件", rule = "非空字符串", requiredness = Requiredness.REQUIRED)
    private String title;

    @ThriftField(7)
    @FieldDoc(description = "事件模式", example = "0", rule = "UNKNOWN(0, \"未知\"), AUTONOMOUS_DRIVING(1, \"自动驾驶状态\"), CLOUD_SOFT_TAKEOVER(2, \"云控软接管状态\"), CLOUD_HARD_TAKEOVER(3, \"云控硬接管状态\"), MANUAL_CONTROL(4, \"手动控制状态\"), NO_CONTROL(5, \"无控制状态\");", requiredness = Requiredness.REQUIRED)
    private Integer driveMode;

    @ThriftField(8)
    @FieldDoc(description = "发现类型", example = "云控", rule = "非空字符串", requiredness = Requiredness.REQUIRED)
    private String discoveryType;

    @ThriftField(9)
    @FieldDoc(description = "处置方式", example = "MRM|1,RESCUE|2", rule = "MRM或RESCUE", requiredness = Requiredness.REQUIRED)
    private Integer handleType;

    @ThriftField(10)
    @FieldDoc(description = "请求帮助时间", example = "YYYY-MM-DD HH:mm:ss", rule = "时间戳格式", requiredness = Requiredness.REQUIRED)
    private String requestHelpTime;

    @ThriftField(11)
    @FieldDoc(description = "开始处理时间", example = "YYYY-MM-DD HH:mm:ss", rule = "时间戳格式", requiredness = Requiredness.REQUIRED)
    private String startHandleTime;

    @ThriftField(12)
    @FieldDoc(description = "完成时间", example = "YYYY-MM-DD HH:mm:ss", rule = "时间戳格式", requiredness = Requiredness.REQUIRED)
    private String finishTime;

    @ThriftField(13)
    @FieldDoc(description = "舆情事件ID", example = "ASDASDASD", rule = "舆情事件ID", requiredness = Requiredness.REQUIRED)
    private Long publicEventId;

    @ThriftField(14)
    @FieldDoc(description = "描述", example = "ADASDSAD", rule = "描述", requiredness = Requiredness.REQUIRED)
    private String description;

    @ThriftField(15)
    @FieldDoc(description = "类别", example = "STOP", rule = "类别", requiredness = Requiredness.REQUIRED)
    private String category;
}