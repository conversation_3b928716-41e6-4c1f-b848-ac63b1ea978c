package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "添加安全区域请求")
public class AdminAddSafetyAreaRequest {

    @ThriftField(1)
    @FieldDoc(
            description = "多边形坐标点列表", rule = "必传，每个点为[经度,纬度]格式",
            example = "[[116.541779,40.071938],[116.541840,40.071871],[116.541861,40.071880],[116.541794,40.071945]]",
            requiredness = Requiredness.REQUIRED
    )
    private List<List<Double>> polygon;

}