package com.sankuai.wallemonitor.risk.center.api.vo;

import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UserNoticeVersionVO {
    /**
     * 是否需要展示用户须知
     */
    private Boolean isDisplay;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 版本内容
     */
    private String versionContext;
}
