package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TypeDoc(description = "手动操作云控呼叫")
public class ManualHandleMrmCallRequest {

    @ThriftField(1)
    @FieldDoc(description = "车架号", rule = "必传", example = "123456", requiredness = Requiredness.REQUIRED)
    private String vin;

    @ThriftField(2)
    @FieldDoc(description = "呼叫原因", rule = "必传", example = "6001", requiredness = Requiredness.REQUIRED)
    private Integer reason;

    @ThriftField(3)
    @FieldDoc(description = "动作 ", rule = "呼叫/取消", example = "", requiredness = Requiredness.REQUIRED)
    private String action;

    @ThriftField(4)
    @FieldDoc(description = "是否需要一直呼叫", rule = "必传", example = "张三", requiredness = Requiredness.REQUIRED)
    private Boolean needCancelCommand;
}
