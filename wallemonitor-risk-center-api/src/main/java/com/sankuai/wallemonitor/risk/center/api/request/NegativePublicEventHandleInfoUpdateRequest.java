package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NegativePublicEventHandleInfoUpdateRequest {

    /**
     * 事件ID
     */
    @ThriftField(1)
    @FieldDoc(description = "事件ID")
    private String eventId;

    /**
     * 解决程度
     */
    @ThriftField(2)
    @FieldDoc(description = "解决程度")
    private Integer handleDegree;

    /**
     * 处置结果
     */
    @ThriftField(3)
    @FieldDoc(description = "处置结果说明")
    private String handleResultDesc;

    /**
     * 处置结果附件
     */
    @ThriftField(4)
    @FieldDoc(description = "处置结果附件")
    private List<String> handleResultDescFileLinkList;


}
