package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "车道查询请求数据传输对象")
public class LaneQueryRequestDTO {

    @ThriftField(1)
    @FieldDoc(description = "距离", example = "100.0", rule = "必传，单位为米", requiredness = Requiredness.REQUIRED)
    private Double distance;

    @ThriftField(2)
    @FieldDoc(description = "经度", example = "114.05", rule = "必传，范围为-180到180", requiredness = Requiredness.REQUIRED)
    private Double longitude;

    @ThriftField(3)
    @FieldDoc(description = "纬度", example = "22.54", rule = "必传，范围为-90到90", requiredness = Requiredness.REQUIRED)
    private Double latitude;

    @ThriftField(4)
    @FieldDoc(description = "hdMapVersion", example = "1.0.0", rule = "必传", requiredness = Requiredness.REQUIRED)
    private String hdMapVersion;

    @ThriftField(5)
    @FieldDoc(description = "id", example = "123", rule = "必传", requiredness = Requiredness.REQUIRED)
    private List<String> idList;

    @ThriftField(6)
    @FieldDoc(description = "地图类型", example = {"object"}, rule = "必传", requiredness = Requiredness.REQUIRED)
    private String mapType = "lane_p";

}