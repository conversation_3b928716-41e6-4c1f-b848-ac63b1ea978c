package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftPageResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventBaseInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventCreateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventDetailRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventDetermineNatureInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventDetermineReasonInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventHandleInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventListRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventReviewInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.response.NegativePublicEventDetailResponse;
import com.sankuai.wallemonitor.risk.center.api.response.NegativePublicEventListResponse;
import java.util.List;

@InterfaceDoc(type = "type = octo.thrift.annotation", displayName = "负外部性事件管理", description = "管理后台接口", scenarios = "后台管理")
@ThriftService
public interface IThriftNegativePublicEventService {

    @MethodDoc(
            displayName = "列表页查询接口",
            description = "列表页查询",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = NegativePublicEventListRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftPageResponse<List<NegativePublicEventListResponse>> list(NegativePublicEventListRequest request);

    @MethodDoc(
            displayName = "事件详情查询接口",
            description = "事件详情查询",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = NegativePublicEventDetailRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<NegativePublicEventDetailResponse> queryEventDetail(NegativePublicEventDetailRequest request);


    @MethodDoc(
            displayName = "负外部性事件创建接口",
            description = "负外部性事件创建接口",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = NegativePublicEventCreateRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> create(NegativePublicEventCreateRequest request);

    @MethodDoc(
            displayName = "基础信息更新接口",
            description = "基础信息部分更新",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = NegativePublicEventBaseInfoUpdateRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> updateBaseInfo(NegativePublicEventBaseInfoUpdateRequest request);

    @MethodDoc(
            displayName = "定位信息更新接口",
            description = "定位信息部分更新",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数",
                            type = NegativePublicEventDetermineNatureInfoUpdateRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> updateDetermineNatureInfo(
            NegativePublicEventDetermineNatureInfoUpdateRequest request);

    @MethodDoc(
            displayName = "定因信息更新接口",
            description = "定因信息部分更新",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数",
                            type = NegativePublicEventDetermineReasonInfoUpdateRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> updateDetermineReasonInfo(
            NegativePublicEventDetermineReasonInfoUpdateRequest request);

    @MethodDoc(
            displayName = "处置信息更新接口",
            description = "处置信息部分更新",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数",
                            type = NegativePublicEventHandleInfoUpdateRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> updateHandleInfo(
            NegativePublicEventHandleInfoUpdateRequest request);

    @MethodDoc(
            displayName = "复盘信息更新接口",
            description = "复盘信息部分更新",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数",
                            type = NegativePublicEventReviewInfoUpdateRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> updateReviewInfo(
            NegativePublicEventReviewInfoUpdateRequest request);


}
