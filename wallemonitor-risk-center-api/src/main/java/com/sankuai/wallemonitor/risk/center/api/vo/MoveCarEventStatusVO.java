package com.sankuai.wallemonitor.risk.center.api.vo;

import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MoveCarEventStatusVO {

    /**
     * 车牌号
     */
    private String vehicleId;

    /**
     * 是否正在挪车
     */
    private Boolean isMoving;

}
