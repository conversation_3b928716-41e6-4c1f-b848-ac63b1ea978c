package com.sankuai.wallemonitor.risk.center.api.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class VehicleRuntimeVO {

    @ThriftField(1)
    @FieldDoc(
            description = "车辆识别码", rule = "必传，17位字符串", example = "LVSHCAMB2PE328901",
            requiredness = Requiredness.REQUIRED
    )
    private String vin;

    @ThriftField(2)
    @FieldDoc(description = "车辆名称", rule = "必传", example = "奔驰C200L", requiredness = Requiredness.REQUIRED)
    private String vehicleName;

    @ThriftField(3)
    @FieldDoc(description = "车辆牌照", rule = "必传", example = "奔驰C200L", requiredness = Requiredness.REQUIRED)
    private String vehicleId;

    @ThriftField(4)
    @FieldDoc(description = "车辆位置信息", rule = "必传", example = "114.12,34.12", requiredness = Requiredness.OPTIONAL)
    private String position;

}
