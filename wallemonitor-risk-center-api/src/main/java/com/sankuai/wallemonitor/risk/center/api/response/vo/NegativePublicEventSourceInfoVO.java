package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NegativePublicEventSourceInfoVO {

    @ThriftField(1)
    @FieldDoc(description = "事件来源（整数表示）")
    private Integer source;

    @ThriftField(2)
    @FieldDoc(description = "事件来源（字符串表示）")
    private String sourceDesc;
}
