package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RiskCaseDisposeInfoVO {

    @ThriftField(1)
    @FieldDoc(description = "云控misId", type = String.class, requiredness = Requiredness.REQUIRED)
    private String mrmMisId;

    @ThriftField(2)
    @FieldDoc(description = "云控坐席号", type = String.class, requiredness = Requiredness.REQUIRED)
    private String mrmSeatNo;

    @ThriftField(3)
    @FieldDoc(description = "云控角色", type = String.class, requiredness = Requiredness.REQUIRED)
    private String role;

    @ThriftField(4)
    @FieldDoc(description = "涉事车辆", type = List.class, requiredness = Requiredness.REQUIRED)
    private String vin;

    @ThriftField(5)
    @FieldDoc(description = "操作类型", type = String.class, requiredness = Requiredness.REQUIRED)
    private String operationType;

    @ThriftField(6)
    @FieldDoc(description = "发生时间戳", type = Long.class, requiredness = Requiredness.REQUIRED)
    private Long timestamp;
}
