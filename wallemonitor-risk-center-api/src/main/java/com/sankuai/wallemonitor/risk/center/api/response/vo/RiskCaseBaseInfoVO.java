package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RiskCaseBaseInfoVO {

    @ThriftField(1)
    @FieldDoc(description = "事件Id", type = String.class, requiredness = Requiredness.REQUIRED)
    private String caseId;

    @ThriftField(2)
    @FieldDoc(description = "风险事件类型", type = Integer.class, requiredness = Requiredness.REQUIRED)
    private Integer type;

    @ThriftField(3)
    @FieldDoc(description = "场地code", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String placeCode;

    @ThriftField(4)
    @FieldDoc(description = "事件状态", type = Integer.class, requiredness = Requiredness.REQUIRED)
    private Integer status;

    @ThriftField(5)
    @FieldDoc(description = "事件上报来源", type = Integer.class, requiredness = Requiredness.REQUIRED)
    private Integer source;

    @ThriftField(6)
    @FieldDoc(description = "发生时间", type = String.class, requiredness = Requiredness.REQUIRED)
    private String occurTime;

    @ThriftField(7)
    @FieldDoc(description = "解除时间", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String closeTime;

    @ThriftField(8)
    @FieldDoc(description = "补充信息", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String extInfo;

    @ThriftField(9)
    @FieldDoc(description = "是否呼叫坐席", type = String.class, requiredness = Requiredness.OPTIONAL)
    private Boolean mrmCalled;

    @ThriftField(10)
    @FieldDoc(description = "呼叫坐席状态", type = Integer.class, requiredness = Requiredness.OPTIONAL)
    private Integer mrmCalledStatus;

    @ThriftField(11)
    @FieldDoc(description = "呼叫云安全状态", type = Integer.class, requiredness = Requiredness.OPTIONAL)
    private Integer callSafety;

    @ThriftField(12)
    @FieldDoc(description = "快速回传数据任务ID", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String fastUploadId;

    @ThriftField(13)
    @FieldDoc(description = "召回时间", type = String.class, requiredness = Requiredness.REQUIRED)
    private String recallTime;

    @ThriftField(14)
    @FieldDoc(description = "事件ID", type = String.class, requiredness = Requiredness.REQUIRED)
    private String eventId;

}
