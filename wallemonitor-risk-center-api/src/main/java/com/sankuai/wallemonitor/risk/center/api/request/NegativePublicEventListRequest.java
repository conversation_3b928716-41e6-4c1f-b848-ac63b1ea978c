package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NegativePublicEventListRequest {

    @ThriftField(1)
    @FieldDoc(description = "创建时段-开始", type = String.class, example = "2024-07-03 12:00:00", requiredness = Requiredness.REQUIRED)
    private String createTimeStart;

    @ThriftField(2)
    @FieldDoc(description = "创建时段-结束", type = String.class, example = "2024-07-03 13:00:00", requiredness = Requiredness.REQUIRED)
    private String createTimeEnd;

    @ThriftField(3)
    @FieldDoc(description = "状态列表", type = List.class, rule = "为空则全选", example = "[10,20]", requiredness = Requiredness.REQUIRED)
    private List<Integer> statusList;

    @ThriftField(4)
    @FieldDoc(description = "事件类型列表", type = List.class, rule = "为空则全选", example = "[1,2]", requiredness = Requiredness.REQUIRED)
    private List<Integer> typeList;

    @ThriftField(5)
    @FieldDoc(description = "来源列表", type = List.class, rule = "为空则全选", example = "[1,2]", requiredness = Requiredness.REQUIRED)
    private List<Integer> sourceList;

    @ThriftField(6)
    @FieldDoc(description = "事件等级列表", type = List.class, rule = "为空则全选", example = "[1,2]", requiredness = Requiredness.REQUIRED)
    private List<Integer> levelList;

    @ThriftField(7)
    @FieldDoc(description = "车辆列表", type = List.class, rule = "为空则全选", example = "['LSTM000031']", requiredness = Requiredness.OPTIONAL)
    private List<String> vinList;

    @ThriftField(8)
    @FieldDoc(description = "问题分类列表", type = List.class, rule = "为空则全选", example = "[1,2]", requiredness = Requiredness.REQUIRED)
    private List<String> categoryList;

    @ThriftField(9)
    @FieldDoc(description = "问题归因列表", type = List.class, rule = "为空则全选", example = "['LSTM000031']", requiredness = Requiredness.OPTIONAL)
    private List<String> reasonList;

    @ThriftField(10)
    @FieldDoc(description = "单页数量", type = Integer.class, rule = "默认10", example = "50", requiredness = Requiredness.REQUIRED)
    private Integer pageSize;

    @ThriftField(11)
    @FieldDoc(description = "页码 ", type = Integer.class, rule = "默认1", example = "1", requiredness = Requiredness.REQUIRED)
    private Integer pageNum;

    @ThriftField(12)
    @FieldDoc(description = "事件性质", type = String.class, rule = "为空则全选", example = "[1,2]", requiredness = Requiredness.REQUIRED)
    private List<Integer> natureList;

    @ThriftField(13)
    @FieldDoc(description = "标题", type = String.class, example = "1", requiredness = Requiredness.REQUIRED)
    private String title;

}
