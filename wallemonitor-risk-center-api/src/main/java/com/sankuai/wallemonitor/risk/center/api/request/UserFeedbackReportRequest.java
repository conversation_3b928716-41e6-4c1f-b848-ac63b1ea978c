package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TypeDoc(description = "用户反馈上报接口")
public class UserFeedbackReportRequest extends WechatBasicFrontRequest{

    @ThriftField(2)
    @FieldDoc(description = "反馈类型 ", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private Integer feedbackType;

    @ThriftField(3)
    @FieldDoc(description = "反馈内容 ", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private String feedbackContent;

    @ThriftField(4)
    @FieldDoc(description = "反馈渠道 ", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private Integer feedbackChannel;

    @ThriftField(5)
    @FieldDoc(description = "附件URL", rule = "必传", example = "", requiredness = Requiredness.OPTIONAL)
    private List<String> urlList;

    @ThriftField(6)
    @FieldDoc(description = "手机号码", rule = "选填", example = "", requiredness = Requiredness.OPTIONAL)
    private String phoneNumber;

    @ThriftField(7)
    @FieldDoc(description = "车牌号", rule = "选填", example = "", requiredness = Requiredness.OPTIONAL)
    private String vehicleId;
}