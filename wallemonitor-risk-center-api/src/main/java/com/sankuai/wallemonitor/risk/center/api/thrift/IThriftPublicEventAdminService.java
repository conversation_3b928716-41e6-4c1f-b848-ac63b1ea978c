package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftPageResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.AdminListRiskCaseRequest;
import com.sankuai.wallemonitor.risk.center.api.request.PublicEventAdminListRequest;
import com.sankuai.wallemonitor.risk.center.api.request.PublicEventAdminSaveRequest;
import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminListPublicEventDetailVO;
import java.util.List;


@InterfaceDoc(type = "type = octo.thrift.annotation", displayName = "管理后台接口", description = "管理后台接口", scenarios = "后台管理")
@ThriftService
public interface IThriftPublicEventAdminService {

    @MethodDoc(
            displayName = "查询舆情事件列表",
            description = "查询舆情事件列表",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = AdminListRiskCaseRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "case列表", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftPageResponse<List<AdminListPublicEventDetailVO>> listPublicEventDetail(
            PublicEventAdminListRequest request);

    @MethodDoc(
            displayName = "更新舆情事件",
            description = "根据请求参数更新舆情事件",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = PublicEventAdminSaveRequest.class, example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "空响应", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> save(PublicEventAdminSaveRequest request);
}
