package com.sankuai.wallemonitor.risk.center.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.wallemonitor.risk.center.api.response.vo.NegativePublicEventOperationRecordVO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NegativePublicEventDetailResponse {

    /**
     * 负外部性事件的唯一标识
     */
    @ThriftField(1)
    private Long id;

    /**
     * 事件ID
     */
    @ThriftField(2)
    private String eventId;

    /**
     * 事件状态
     */
    @ThriftField(3)
    private Integer status;

    /**
     * 事件来源
     */
    @ThriftField(4)
    private List<Integer> sourceList;

    /**
     * 事件类型
     */
    @ThriftField(5)
    private Integer type;

    /**
     * 事件创建时间
     */
    @ThriftField(6)
    private String createTime;

    /**
     * 发生时间
     */
    @ThriftField(7)
    private String occurTime;

    /**
     * 感知时间
     */
    @ThriftField(8)
    private String perceiveTime;

    /**
     * 省
     */
    @ThriftField(9)
    private String province;

    /**
     * 城市
     */
    @ThriftField(10)
    private String city;

    /**
     * 区
     */
    @ThriftField(11)
    private String district;

    /**
     * 详细地址
     */
    @ThriftField(12)
    private String location;

    /**
     * 关联的文件链接
     */
    @ThriftField(13)
    private List<String> relatedFileLinks;

    /**
     * 群ID
     */
    @ThriftField(14)
    private String groupId;

    /**
     * 事故工单链接
     */
    @ThriftField(15)
    private String accidentOrderLink;

    /**
     * 事件性质
     */
    @ThriftField(16)
    private Integer nature;

    /**
     * 问题分类
     */
    @ThriftField(17)
    private String category;

    /**
     * 事件等级
     */
    @ThriftField(18)
    private Integer level;

    /**
     * 情况说明
     */
    @ThriftField(19)
    private String conditionDesc;

    /**
     * 处置人
     */
    @ThriftField(20)
    private List<String> handlers;

    /**
     * 网络昵称
     */
    @ThriftField(21)
    private String internetNickname;

    /**
     * 联系方式
     */
    @ThriftField(22)
    private String contactInfo;

    /**
     * 关联的车架号列表
     */
    @ThriftField(23)
    private List<String> relatedVins;

    /**
     * 问题归因
     */
    @ThriftField(24)
    private List<String> reasonList;

    /**
     * 排查结果
     */
    @ThriftField(25)
    private String checkResult;

    /**
     * 解决程度
     */
    @ThriftField(26)
    private Integer handleDegree;

    /**
     * 处理结果描述
     */
    @ThriftField(27)
    private String handleResultDesc;

    /**
     * 复盘信息
     */
    @ThriftField(28)
    private String reviewInfo;

    /**
     * 其他原因描述
     */
    @ThriftField(29)
    private String otherReasonDesc;

    /**
     * 其他分类描述
     */
    @ThriftField(30)
    private String otherCategoryDesc;

    /**
     * 标题
     */
    @ThriftField(31)
    private String title;

    /**
     * 问题描述
     */
    @ThriftField(32)
    private String eventDesc;

    /**
     * 处理结果描述文件链接
     */
    @ThriftField(33)
    private List<String> handleResultDescFileLinkList;

    /**
     * 情况描述相关文件链接列表
     */
    @ThriftField(34)
    private List<String> conditionDescRelatedFileLinkList;

    /**
     * 操作信息
     */
    @ThriftField(35)
    private List<NegativePublicEventOperationRecordVO> operationRecordList;


}
