package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TypeDoc(description = "手动风险案例操作请求")
public class ManualRiskCaseOperateRequest {

    @ThriftField(1)
    @FieldDoc(description = "操作者", rule = "必传", example = "张三", requiredness = Requiredness.REQUIRED)
    private String operator;

    @ThriftField(2)
    @FieldDoc(description = "风险ID列表 ", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private List<String> caseIdList;
}