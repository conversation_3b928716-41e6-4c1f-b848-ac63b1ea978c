package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AdminListRiskCaseRequest {

    @ThriftField(1)
    @FieldDoc(description = "创建时段-开始", type = String.class, example = "2024-07-03 12:00:00", requiredness = Requiredness.REQUIRED)
    private String createTimeStart;

    @ThriftField(2)
    @FieldDoc(description = "创建时段-结束", type = String.class, example = "2024-07-03 13:00:00", requiredness = Requiredness.REQUIRED)
    private String createTimeEnd;

    @ThriftField(3)
    @FieldDoc(description = "状态列表", type = List.class, rule = "为空则全选", example = "[10,20]", requiredness = Requiredness.REQUIRED)
    private List<Integer> statusList;

    @ThriftField(4)
    @FieldDoc(description = "事件类型列表", type = List.class, rule = "为空则全选", example = "[1,2]", requiredness = Requiredness.REQUIRED)
    private List<Integer> typeList;

    @ThriftField(5)
    @FieldDoc(description = "来源列表", type = List.class, rule = "为空则全选", example = "[1,2]", requiredness = Requiredness.REQUIRED)
    private List<Integer> sourceList;

    @ThriftField(6)
    @FieldDoc(description = "场地列表", type = List.class, rule = "为空则全选", example = "['huallikan','shunyi']", requiredness = Requiredness.REQUIRED)
    private List<String> placeCodeList;

    @ThriftField(7)
    @FieldDoc(description = "单页数量", type = Integer.class, rule = "默认100", example = "50", requiredness = Requiredness.REQUIRED)
    private Integer pageSize;

    @ThriftField(8)
    @FieldDoc(description = "页码 ", type = Integer.class, rule = "默认1", example = "1", requiredness = Requiredness.REQUIRED)
    private Integer pageNum;

    @ThriftField(9)
    @FieldDoc(description = "车辆列表", type = List.class, rule = "传入车辆", example = "['LSTM000031']", requiredness = Requiredness.OPTIONAL)
    private List<String> vinList;

    @ThriftField(10)
    @FieldDoc(description = "分类", type = List.class, rule = "case分类", example = "['BAD']", requiredness = Requiredness.OPTIONAL)
    private List<String> categoryList;

    @ThriftField(11)
    @FieldDoc(description = "等级", type = List.class, rule = "case等级", example = "[1]", requiredness = Requiredness.OPTIONAL)
    private List<Integer> levelList;

    @ThriftField(12)
    @FieldDoc(description = "持续时间大于", type = Integer.class, rule = "持续时间大于", example = "123123", requiredness = Requiredness.OPTIONAL)
    private Integer durationGreatThan;

    @ThriftField(13)
    @FieldDoc(description = "用车目的列表", type = List.class, rule = "为空则全选", example = "['业务运营']", requiredness = Requiredness.REQUIRED)
    private List<String> purposeList;

    @ThriftField(13)
    @FieldDoc(description = "vhr模式列表", type = List.class, rule = "为空则全选", example = "['=1','>1']", requiredness = Requiredness.REQUIRED)
    private List<String> vhrModeList;

    @ThriftField(14)
    @FieldDoc(description = "是否呼叫坐席", type = Boolean.class, requiredness = Requiredness.OPTIONAL)
    private Boolean mrmCalled;

    @ThriftField(15)
    @FieldDoc(description = "标注场景", type = List.class, rule = "标注场景", example = "[]", requiredness = Requiredness.OPTIONAL)
    private List<String> subCategoryList;

    @ThriftField(16)
    @FieldDoc(description = "呼叫坐席状态列表", type = List.class, rule = "标注场景", example = "[]", requiredness = Requiredness.OPTIONAL)
    private List<Integer> mrmCalledList;

    @ThriftField(17)
    @FieldDoc(description = "归属问题", type = List.class, rule = "归属问题", example = "[]", requiredness = Requiredness.OPTIONAL)
    private List<String> problemList;

    @ThriftField(18)
    @FieldDoc(description = "云安全呼叫状态列表", type = List.class, rule = "状态值", example = "[]", requiredness = Requiredness.OPTIONAL)
    private List<Integer> callSafetyList;

    @ThriftField(19)
    @FieldDoc(description = "持续时间大于", type = Integer.class, rule = "持续时间大于", example = "123123", requiredness = Requiredness.OPTIONAL)
    private Integer recallDurationGreatThan;

    @ThriftField(20)
    @FieldDoc(description = "首次标注场景", type = List.class, rule = "首次标注场景", example = "[]", requiredness = Requiredness.OPTIONAL)
    private List<String> firstSubCategoryList;

    @ThriftField(21)
    @FieldDoc(description = "云控是否介入/接管", type = List.class, rule = "", example = "[]", requiredness = Requiredness.OPTIONAL)
    private Boolean mrmIntervened;

    @ThriftField(22)
    @FieldDoc(description = "caseId列表", type = List.class, requiredness = Requiredness.OPTIONAL)
    private List<String> caseIdList;

    @ThriftField(23)
    @FieldDoc(description = "poiName列表", type = List.class, requiredness = Requiredness.OPTIONAL)
    private List<String> poiNameList;

    @ThriftField(24)
    @FieldDoc(description = "最终标注人列表", type = List.class, requiredness = Requiredness.OPTIONAL)
    private List<String> finalMarkerList;

    @ThriftField(25)
    @FieldDoc(description = "自动标注轮次", type = List.class, requiredness = Requiredness.OPTIONAL)
    private List<Integer> markRoundList;

    @ThriftField(26)
    @FieldDoc(description = "标注版本", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String version;

    @ThriftField(27)
    @FieldDoc(
            description = "位置(WGS84)", type = String.class, example = {"114.07,22.62"},
            requiredness = Requiredness.OPTIONAL
    )
    private String position;

    @ThriftField(28)
    @FieldDoc(description = "搜索范围(m)", type = Double.class, example = {
            "10.0"}, requiredness = Requiredness.OPTIONAL)
    private Double searchRange;

    @ThriftField(29)
    @FieldDoc(description = "呼叫原因列表", type = List.class, requiredness = Requiredness.OPTIONAL)
    private List<String> callMrmReasonList;

    @ThriftField(30)
    @FieldDoc(description = "车型", type = List.class, requiredness = Requiredness.OPTIONAL)
    private List<String> vehicletypeList;

}
