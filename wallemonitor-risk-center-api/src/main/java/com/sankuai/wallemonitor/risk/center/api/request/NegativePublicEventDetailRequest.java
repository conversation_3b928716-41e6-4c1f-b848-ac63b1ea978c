package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NegativePublicEventDetailRequest {

    @ThriftField(1)
    @FieldDoc(description = "自增ID", type = String.class, example = "1", requiredness = Requiredness.REQUIRED)
    private Long id;

    @ThriftField(2)
    @FieldDoc(description = "事件ID", type = String.class, example = "uuid", requiredness = Requiredness.REQUIRED)
    private String eventId;

}
