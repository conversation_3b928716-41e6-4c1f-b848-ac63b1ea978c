package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RiskCaseMarkInfoVO {

    @ThriftField(1)
    @FieldDoc(description = "事件Id", type = String.class, requiredness = Requiredness.REQUIRED)
    private Integer riskLevel;

    @ThriftField(2)
    @FieldDoc(description = "类别", type = String.class, example = "GOOD", requiredness = Requiredness.OPTIONAL)
    private String category;

    @ThriftField(3)
    @FieldDoc(description = "子类别", type = String.class, example = "OTHER", requiredness = Requiredness.OPTIONAL)
    private String subCategory;

    @ThriftField(4)
    @FieldDoc(description = "最近操作人misId", type = String.class, example = "zhangsan", requiredness = Requiredness.OPTIONAL)
    private String lastOperator;

    @ThriftField(5)
    @FieldDoc(description = "补充信息", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String extInfo;

    @ThriftField(6)
    @FieldDoc(description = "停滞原因", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String improperStrandingReason;

    @ThriftField(7)
    @FieldDoc(description = "类别（首次）", type = String.class, example = "GOOD", requiredness = Requiredness.OPTIONAL)
    private String firstCategory;

    @ThriftField(8)
    @FieldDoc(description = "场景（首次）", type = String.class, example = "OTHER", requiredness = Requiredness.OPTIONAL)
    private String firstSubCategory;

    @ThriftField(9)
    @FieldDoc(description = "标注来源（首次）", type = String.class, example = "zhangsan", requiredness = Requiredness.OPTIONAL)
    private String firstOperator;

    @ThriftField(10)
    @FieldDoc(description = "标注轮次", type = Integer.class, example = "1", requiredness = Requiredness.OPTIONAL)
    private Integer round;

}
