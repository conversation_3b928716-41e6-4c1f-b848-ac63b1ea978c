package com.sankuai.wallemonitor.risk.center.api.response.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * {"vin":"LMTZSV023MC063495","vehicleId":"M2326","vehicleName":"s20-178","purpose":"","position":{"latitude":22.71995,"longitude":114.38237,"coordinateSystem":"WGS84"},"placeCode":"hualikan","autocarVersion":"56.9"}
 *
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RiskCaseVehicleVO {

    @ThriftField(1)
    @FieldDoc(description = "车架号", type = String.class, requiredness = Requiredness.REQUIRED)
    private String vin;

    @ThriftField(2)
    @FieldDoc(description = "车辆名称", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String vehicleName;

    @ThriftField(3)
    @FieldDoc(description = "额外信息", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String extInfo;

    @ThriftField(4)
    @FieldDoc(description = "请求坐席时间", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String requestSeatTime;

    @ThriftField(5)
    @FieldDoc(description = "请求连车时间", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String seatConnectTime;

    @ThriftField(6)
    @FieldDoc(description = "取消坐席请求时间", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String cancelSeatTime;

    @ThriftField(7)
    @FieldDoc(description = "车辆关联信息额外字段", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String extraInfo;

    @ThriftField(8)
    @FieldDoc(description = "坐席介入时间", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String seatInterventionTime;

    @ThriftField(9)
    @FieldDoc(description = "烽火台呼叫坐席连入时间", type = String.class, requiredness = Requiredness.OPTIONAL)
    private String seatResponseTime;
}
