package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;

import java.util.List;

@InterfaceDoc(type = "type = octo.thrift.annotation", displayName = "车辆灰度策略过滤接口", description = "车辆灰度策略过滤接口", scenarios = "车辆灰度策略管理")
@ThriftService
public interface IThriftVehicleGrayStrategyService {

    @MethodDoc(
            displayName = "车辆灰度策略过滤接口",
            description = "根据配置的灰度策略过滤车辆，返回符合条件的车辆VIN码列表",
            returnValueDescription = "符合灰度策略的车辆VIN码列表",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "车辆VIN码列表", example = {"[\"VIN001\", \"VIN002\"]"})
            }
    )
    @ThriftMethod
    EveThriftResponse<List<String>> getVehicleGrayStrategyFilter();
} 