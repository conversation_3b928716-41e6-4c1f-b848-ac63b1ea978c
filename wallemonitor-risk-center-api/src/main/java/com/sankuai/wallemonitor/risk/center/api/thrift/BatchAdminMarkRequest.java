package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class BatchAdminMarkRequest {

    @ThriftField(1)
    @FieldDoc(description = "caseIdList ", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private List<String> caseIdList;

    @ThriftField(2)
    @FieldDoc(description = "类别", example = "GOOD", requiredness = Requiredness.OPTIONAL)
    private String category;

    @ThriftField(3)
    @FieldDoc(description = "子类别", example = "ON_PACKING_AREA", requiredness = Requiredness.OPTIONAL)
    private String subCategory;

}
