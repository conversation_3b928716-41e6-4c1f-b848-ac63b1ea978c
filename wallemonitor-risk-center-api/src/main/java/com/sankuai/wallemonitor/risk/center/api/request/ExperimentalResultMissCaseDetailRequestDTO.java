package com.sankuai.wallemonitor.risk.center.api.request;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实验结果分场景数据
 *
 * <AUTHOR>
 * @date 2024/11/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@TypeDoc(description = "实验结果分场景数据")
public class ExperimentalResultMissCaseDetailRequestDTO {

    @ThriftField(1)
    @FieldDoc(description = "事件ID", example = "", requiredness = Requiredness.REQUIRED)
    private String caseId;

    @ThriftField(2)
    @FieldDoc(description = "真值", example = "ISCheckCategoryEnum.getSubcategory()", requiredness = Requiredness.REQUIRED)
    private String trueCategory;

    @ThriftField(3)
    @FieldDoc(description = "漏召次数", example = "", requiredness = Requiredness.REQUIRED)
    private Integer missCount;

    @ThriftField(4)
    @FieldDoc(description = "识别过程信息", example = "", requiredness = Requiredness.REQUIRED)
    private List<IdentifyProcessInfoDTO> identifyProcessInfo;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ThriftStruct
    public static class IdentifyProcessInfoDTO {

        @ThriftField(1)
        @FieldDoc(description = "漏召类型", example = "ISCheckCategoryEnum.getSubcategory()", requiredness = Requiredness.REQUIRED)
        private String category;

        @ThriftField(2)
        @FieldDoc(description = "漏召次数", example = "", requiredness = Requiredness.REQUIRED)
        private Integer count;

        @ThriftField(3)
        @FieldDoc(description = "过程详情", example = "", requiredness = Requiredness.REQUIRED)
        private Map<String, IdentifyProcessDetailDTO> detail;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ThriftStruct
    public static class IdentifyProcessDetailDTO {

        @ThriftField(1)
        @FieldDoc(description = "位置信息", example = "", requiredness = Requiredness.REQUIRED)
        String info;

        @ThriftField(2)
        @FieldDoc(description = "图片地址", example = "", requiredness = Requiredness.REQUIRED)
        String picUrl;
    }
}


