package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AdminMarkRequest {

    @ThriftField(1)
    @FieldDoc(description = "caseId ", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private String caseId;

    @ThriftField(2)
    @FieldDoc(description = "风险等级", example = "1", requiredness = Requiredness.OPTIONAL)
    private Integer level;

    @ThriftField(3)
    @FieldDoc(description = "类别", example = "GOOD", requiredness = Requiredness.OPTIONAL)
    private String category;

    @ThriftField(4)
    @FieldDoc(description = "子类别", example = "ON_PACKING_AREA", requiredness = Requiredness.OPTIONAL)
    private String subCategory;

    @ThriftField(5)
    @FieldDoc(description = "补充说明 ", example = "这是一个xx事件", requiredness = Requiredness.OPTIONAL)
    private String desc;

    @ThriftField(6)
    @FieldDoc(description = "是否解除case ", example = "True", requiredness = Requiredness.OPTIONAL)
    private Boolean closeCase;
}