package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "更新 case信息")
public class AdminUpdateCaseRequest {
    // 发生时间
    @FieldDoc(description = "发生时间", requiredness = Requiredness.REQUIRED)
    @ThriftField(1)
    private String occurTime;

    //caseId
    @FieldDoc(description = "caseId", requiredness = Requiredness.REQUIRED)
    @ThriftField(2)
    private String caseId;

    //eventId
    @FieldDoc(description = "eventId", requiredness = Requiredness.REQUIRED)
    @ThriftField(3)
    private String eventId;
}