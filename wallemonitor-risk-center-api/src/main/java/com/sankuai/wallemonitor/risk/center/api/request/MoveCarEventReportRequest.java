package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TypeDoc(description = "挪车事件上报请求")
public class MoveCarEventReportRequest extends WechatBasicFrontRequest {

    @ThriftField(1)
    @FieldDoc(description = "车牌号", rule = "必传", example = "张三", requiredness = Requiredness.REQUIRED)
    private String vehicleId;

    @ThriftField(2)
    @FieldDoc(description = "事件类型", rule = "必传", example = "32", requiredness = Requiredness.REQUIRED)
    private Integer eventType;

    @ThriftField(3)
    @FieldDoc(description = "车辆位置", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private String carPosition;

    @ThriftField(4)
    @FieldDoc(description = "车辆经纬度信息", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private String carPositionGps;

    @ThriftField(5)
    @FieldDoc(description = "挪车原因", rule = "必传", example = "车辆停放不当", requiredness = Requiredness.REQUIRED)
    private String moveCarReason;

    @ThriftField(6)
    @FieldDoc(description = "上报人手机号", rule = "必传", example = "178XXXXXXXX", requiredness = Requiredness.REQUIRED)
    private String phoneNumber;

    @ThriftField(7)
    @FieldDoc(description = " 现场照片url集合", rule = "必传", example = "", requiredness = Requiredness.REQUIRED)
    private List<String> urlList;
}
