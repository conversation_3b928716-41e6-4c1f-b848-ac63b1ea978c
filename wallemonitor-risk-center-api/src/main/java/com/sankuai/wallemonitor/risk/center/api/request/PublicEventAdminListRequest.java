package com.sankuai.wallemonitor.risk.center.api.request;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PublicEventAdminListRequest {

    @ThriftField(1)
    @FieldDoc(description = "创建时段-开始", type = String.class, example = "2024-07-03 12:00:00", requiredness = Requiredness.REQUIRED)
    private String occurTimeStart;

    @ThriftField(2)
    @FieldDoc(description = "创建时段-结束", type = String.class, example = "2024-07-03 13:00:00", requiredness = Requiredness.REQUIRED)
    private String occurTimeEnd;

    @ThriftField(3)
    @FieldDoc(description = "责任方列表", type = List.class, rule = "为空则全选", requiredness = Requiredness.REQUIRED)
    private List<String> responsiblePartyList;

    @ThriftField(4)
    @FieldDoc(description = "事件ID", type = String.class, rule = "事件ID", requiredness = Requiredness.REQUIRED)
    private Long publicEventId;

    @ThriftField(5)
    @FieldDoc(description = "单页数量", type = Integer.class, rule = "默认100", example = "50", requiredness = Requiredness.REQUIRED)
    private Integer pageSize;

    @ThriftField(6)
    @FieldDoc(description = "页码 ", type = Integer.class, rule = "默认1", example = "1", requiredness = Requiredness.REQUIRED)
    private Integer pageNum;

    @ThriftField(7)
    @FieldDoc(description = "类别", type = List.class, rule = "为空则全选", requiredness = Requiredness.REQUIRED)
    private List<String> categoryList;

}
