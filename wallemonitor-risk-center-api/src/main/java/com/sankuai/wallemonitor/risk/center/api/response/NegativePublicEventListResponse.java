package com.sankuai.wallemonitor.risk.center.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.wallemonitor.risk.center.api.response.vo.NegativePublicEventSourceInfoVO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NegativePublicEventListResponse {

    /**
     * 负外部性事件的唯一标识
     */
    @ThriftField(1)
    private Long id;

    /**
     * 事件ID
     */
    @ThriftField(2)
    private String eventId;

    /**
     * 事件创建时间
     */
    @ThriftField(3)
    private String createTime;

    /**
     * 事件标题
     */
    @ThriftField(4)
    private String title;

    /**
     * 事件类型（整数表示）
     */
    @ThriftField(5)
    private Integer type;

    /**
     * 事件类型的描述
     */
    @ThriftField(6)
    private String typeDesc;

    /**
     * 事件来源（整数表示）
     */
    @ThriftField(7)
    private List<NegativePublicEventSourceInfoVO> sourceInfoList;

    /**
     * 事件详细描述
     */
    @ThriftField(8)
    private String eventDesc;

    /**
     * 事件状态（整数表示）
     */
    @ThriftField(9)
    private Integer status;

    /**
     * 事件状态的描述
     */
    @ThriftField(10)
    private String statusDesc;

}
