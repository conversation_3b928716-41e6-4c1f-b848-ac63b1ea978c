package com.sankuai.wallemonitor.risk.center.api.thrift;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.UserConfirmNoticeRequest;
import com.sankuai.wallemonitor.risk.center.api.request.WechatBasicFrontRequest;
import com.sankuai.wallemonitor.risk.center.api.vo.UserNoticeVersionVO;

@InterfaceDoc(type = "type = octo.thrift.annotation", displayName = "用户须知管理接口", description = "用户须知管理接口", scenarios = "用户须知管理")
@ThriftService
public interface IThriftUserNoticeAdminService {

    @MethodDoc(
            displayName = "获取用户须知",
            description = "获取用户须知",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<UserNoticeVersionVO> getUserNotice(WechatBasicFrontRequest request);

    @MethodDoc(
            displayName = "确认阅读用户须知",
            description = "确认阅读用户须知",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", example = "{}")
            },
            returnValueDescription = "返回值描述",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", description = "数据", example = "{}")
            }
    )
    @ThriftMethod
    EveThriftResponse<EmptyResponse> confirmUserNotice(UserConfirmNoticeRequest request);
}