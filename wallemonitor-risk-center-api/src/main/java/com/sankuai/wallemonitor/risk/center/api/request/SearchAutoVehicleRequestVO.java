package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "搜索自动驾驶车辆请求")
public class SearchAutoVehicleRequestVO {
    @ThriftField(1)
    @FieldDoc(description = "搜索范围(单位:公里)", rule = "必传,范围0-100", example = "5.0", requiredness = Requiredness.REQUIRED)
    private Double range;

    @ThriftField(2)
    @FieldDoc(
            description = "搜索位置", rule = "必传,格式为'纬度,经度'", example = "30.5508,104.0623",
            requiredness = Requiredness.REQUIRED
    )
    private String location;
}
