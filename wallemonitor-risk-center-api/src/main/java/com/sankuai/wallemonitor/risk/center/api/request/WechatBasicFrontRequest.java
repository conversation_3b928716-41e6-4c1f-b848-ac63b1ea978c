package com.sankuai.wallemonitor.risk.center.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import java.io.Serializable;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@ToString
public class WechatBasicFrontRequest implements Serializable {

    /**
     * 用户登陆态
     */
    @ThriftField(1)
    @FieldDoc(description = "用户登陆态")
    private String token;


    public String getToken() {
        return Optional.ofNullable(token).orElse("");
    }

}
