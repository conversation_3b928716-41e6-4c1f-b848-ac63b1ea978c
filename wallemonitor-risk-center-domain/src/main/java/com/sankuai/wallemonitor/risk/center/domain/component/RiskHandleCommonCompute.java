package com.sankuai.wallemonitor.risk.center.domain.component;

import com.sankuai.wallemonitor.risk.center.infra.enums.DriverModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.DriveModeRecordVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.DriveModeRecordVTO.DriveModeRecord;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 风险处理通用计算类
 */
@Component
@Slf4j
public class RiskHandleCommonCompute {

    /**
     * 计算是否被人工主动停靠
     *
     * @param driveModeRecordVTO
     * @return
     * @see https://km.sankuai.com/collabpage/2516179260 规则：1.停滞时刻是坐席控车，随后切到无控制（占比90%+）；2、停滞过程中截止到当前是云控控车状态
     */
    public Boolean calcIsParkingByManual(DriveModeRecordVTO driveModeRecordVTO, Date strandingTime, String caseId) {
        if (Objects.isNull(driveModeRecordVTO) || CollectionUtils.isEmpty(driveModeRecordVTO.getRecordList())) {
            return false;
        }
        List<DriveModeRecord> driveModeRecordList = driveModeRecordVTO.getRecordList();
        // 查询距离停滞时刻最近的前后两个元素
        Optional<DriveModeRecord> beforeRecord = driveModeRecordList.stream()
                .filter(record -> record.getChangeTime().before(strandingTime))
                .max(Comparator.comparing(record -> record.getChangeTime()));

        Optional<DriveModeRecord> afterRecord = driveModeRecordList.stream()
                .filter(record -> record.getChangeTime().after(strandingTime))
                .min(Comparator.comparing(record -> record.getChangeTime()));

        DriveModeRecord beforeModeRecord = beforeRecord.orElse(null);
        DriveModeRecord afterModeRecord = afterRecord.orElse(null);

        // 如果驾驶状态变更发生在停滞之前
        if (Objects.nonNull(beforeModeRecord)) {
            if ((DriverModeEnum.isManualDriverMode(beforeModeRecord.getPreMode())
                    && DriverModeEnum.isUncontrolledDriverMode(beforeModeRecord.getCurMode()))
                    || DriverModeEnum.isManualDriverMode(beforeModeRecord.getCurMode())) {
                log.info(
                        "calcIsParkingByManual, before, caseId:{} 主动停靠, beforeModeRecord = {}, afterModeRecord = {}",
                        caseId, beforeRecord, afterRecord);
                return true;
            }
        }
        // 如果驾驶状态变更发生在停滞之后
        if (Objects.nonNull(afterModeRecord)) {
            if (DriverModeEnum.isManualDriverMode(afterModeRecord.getPreMode())
                    && DriverModeEnum.isUncontrolledDriverMode(afterModeRecord.getCurMode())) {
                log.info(
                        "calcIsParkingByManual, after, caseId:{} 主动停靠, beforeModeRecord = {}, afterModeRecord = {}",
                        caseId, beforeRecord, afterRecord);
                return true;
            }
        }
        return false;
    }

}
