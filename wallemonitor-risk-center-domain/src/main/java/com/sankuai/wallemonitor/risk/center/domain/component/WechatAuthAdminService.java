package com.sankuai.wallemonitor.risk.center.domain.component;

import com.sankuai.walleeve.dto.TokenCheckDTO;
import com.sankuai.walleeve.utils.JwtUtil;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.WeChatServiceAdaptor;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppPropertiesConstant;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteErrorException;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WechatAuthAdminService {

    @Resource
    private WeChatServiceAdaptor weChatServiceAdaptor;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Value(AppPropertiesConstant.WECHAT_AUTH_SECRET)
    private String wechatAuthSecret;

    /**
     * 检查token的有效性和主题信息
     *
     * @param token 待检查的token
     * @return TokenCheckDTO对象，包含token的有效性和主题信息
     */
    public TokenCheckDTO checkToken(String token) {
        log.info("token = {}, WAS = {}", token, wechatAuthSecret);
        TokenCheckDTO tokenCheckDTO = JwtUtil.getTokenValidAndSubject(token, wechatAuthSecret);
        log.info("getTokenValidAndSubject, tokenCheckDTO = {}", tokenCheckDTO);
        return tokenCheckDTO;
    }

    /**
     * 根据微信code获取用户的唯一openId，并生成对应的登陆态token
     *
     * @param code 微信code
     * @return 生成的登陆态token
     * @throws RemoteErrorException 远程调用异常
     */
    public String getTokenByCode(String code) throws RemoteErrorException {
        // 1 根据code从微信获取唯一openId
        String openId = weChatServiceAdaptor.getSessionKeyAndOpenid(code);
        // 2 根据唯一ID生成登陆态 token, 过期时间可配置
        String token = JwtUtil.generateToken(openId, lionConfigRepository.getTokenValidMillis(),
                wechatAuthSecret);
        log.info("getTokenByCode, code = {}, openId = {}, token ={}", code, openId, token);
        return token;
    }

}
