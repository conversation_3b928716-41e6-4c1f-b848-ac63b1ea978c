package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertImageTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 时间图片字段处理器
 * 处理图片URL生成逻辑
 */
@Component
@Slf4j
public class OccurTimeImgFieldProcessor implements TemplateFieldProcessor {

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {

        if (CollectionUtils.isEmpty(riskCaseList)) {
            log.warn("风险事件列表为空，无法生成图片URL");
            template.setShowOccurTimeImg(false);
            return;
        }

        try {
            // 取最后一个case的图片
            RiskCaseDO lastCase = riskCaseList.get(riskCaseList.size() - 1);
            VehicleInfoDO vehicleInfo = context.getVehicleInfo(lastCase.getCaseId());

            if (vehicleInfo == null || StringUtils.isBlank(vehicleInfo.getVin())) {
                log.warn("无法生成图片URL: caseId={}", lastCase.getCaseId());
                template.setShowOccurTimeImg(false);
                return;
            }
            String imageUrl = generateImageUrl(alertTemplate, vehicleInfo.getVin(), lastCase.getOccurTime());
            if (StringUtils.isBlank(imageUrl)) {
                log.warn("无法生成图片URL: caseId={}", lastCase.getCaseId());
                template.setShowOccurTimeImg(false);
                return;
            }
            template.setOccurTimeImg(imageUrl);
            template.setShowOccurTimeImg(true);
        } catch (Exception e) {
            log.error("处理图片字段失败", e);
            template.setShowOccurTimeImg(false);
        }
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.OCCUR_TIME_IMG.getCode().equals(fieldCode);
    }

    /**
     * 生成图片URL
     */
    private String generateImageUrl(AlertTemplateConfigDTO alertTemplate, String vin, Date occurTime) {
        try {
            AlertImageTypeEnum imageType = AlertImageTypeEnum.getByType(alertTemplate.getImageType());
            if (imageType == null) {
                log.warn("不支持的图片类型: {}", alertTemplate.getImageType());
                return null;
            }
            return imageType.getUrl(vin, occurTime);
        } catch (Exception e) {
            log.error("生成图片URL失败: vin={}, occurTime={}, imageType={}", vin, occurTime,
                    alertTemplate.getImageType(), e);
            return null;
        }
    }
} 