package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 按钮文本字段处理器
 * 处理按钮文本的变量渲染
 */
@Component
@Slf4j
public class ButtonTextFieldProcessor implements TemplateFieldProcessor {

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {
        
        // 从配置中获取按钮文本模板
        String buttonTextTemplate = getButtonTextFromConfig(alertTemplate);
        
        if (StringUtils.isBlank(buttonTextTemplate)) {
            log.warn("按钮文本模板为空，使用默认文本");
            template.setButtonTex("没有配置按钮文字");
            return;
        }
        template.setButtonTex(buttonTextTemplate);
        template.setShowButton(true);
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.BUTTON_TEXT.getCode().equals(fieldCode);
    }

    /**
     * 从配置中获取按钮文本模板
     */
    private String getButtonTextFromConfig(AlertTemplateConfigDTO alertTemplate) {
        if (alertTemplate == null || StringUtils.isBlank(alertTemplate.getButtonText())) {
            return null;
        }
        return alertTemplate.getButtonText();
    }
  
}
