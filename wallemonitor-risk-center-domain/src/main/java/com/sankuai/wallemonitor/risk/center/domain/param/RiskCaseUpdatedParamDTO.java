package com.sankuai.wallemonitor.risk.center.domain.param;

import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseMessageDTOConvert.VehicleEventDataMessageExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import java.util.List;

import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCaseUpdatedParamDTO {

    /**
     * 事件ID
     */
    private String eventId;
    /**
     * 来源
     */
    private RiskCaseSourceEnum source;
    /**
     * 类型
     */
    private RiskCaseTypeEnum type;
    /**
     * 状态
     */
    private RiskCaseStatusEnum status;
    /**
     * 车辆列表
     */
    private List<String> vinList;
    /**
     * 事件关联的traceId
     */
    private String traceId;

    /**
     * 根据状态，发生时间或关闭时间
     */
    private Long timestamp;

    /**
     * 解除时间
     */
    private Long closeTime;

    /**
     * 召回时间
     */
    private Long recallTime;

    /**
     * 并排开始时间
     */
    private String sideBySideTimestamp;

    /**
     * 扩展信息
     */
    private VehicleEventDataMessageExtInfoDTO messageExtInfo;

    /**
     * 上游直接生成好caseId
     */
    private String caseId;

    /**
     *  事件位置
     * */
    private PositionDO positionDO;

    /**
     * 事件POI
     * */
    private String poiName;
}
