package com.sankuai.wallemonitor.risk.center.domain.config;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateStrategy;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 聚合策略配置类
 */
@Configuration
public class AggregateStrategyConfig {

    @Resource
    private List<AggregateStrategy> aggregateStrategies;

    /**
     * 创建聚合策略映射
     */
    @Bean
    public Map<String, AggregateStrategy> aggregateStrategyMap() {
        Map<String, AggregateStrategy> strategyMap = new HashMap<>();
        for (AggregateStrategy strategy : aggregateStrategies) {
            strategyMap.put(strategy.getAlertPolicy().getPolicy(), strategy);
        }
        return strategyMap;
    }
} 