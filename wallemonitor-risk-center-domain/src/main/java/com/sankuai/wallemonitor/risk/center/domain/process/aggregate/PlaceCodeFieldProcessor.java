package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.google.common.base.Joiner;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AggregateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.DxRichTextUtils;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 场地名称字段处理器
 */
@Component
public class PlaceCodeFieldProcessor implements TemplateFieldProcessor {

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {
        if (CollectionUtils.isEmpty(riskCaseList)) {
            return;
        }
        List<String> placeCodeList = riskCaseList.stream()
                .map(this::buildPlaceCode)
                .collect(Collectors.toList());

        // 根据聚合字段进行优化
        String placeCodeValue = optimizeForAggregation(placeCodeList, aggregateBy);

        template.setPlaceCode(placeCodeValue);
        template.setShowPlaceCode(true);
    }

    /**
     * 构建场地名称
     */
    private String buildPlaceCode(RiskCaseDO caseDO) {
        return StringUtils.defaultIfBlank(caseDO.getPlaceCode(), CommonConstant.UNKNOWN);
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.PLACE_CODE.getCode().equals(fieldCode);
    }

    /**
     * 根据聚合字段优化显示内容
     */
    private String optimizeForAggregation(List<String> placeCodeList, List<String> aggregateBy) {
        if (CollectionUtils.isEmpty(placeCodeList)) {
            return CommonConstant.UNKNOWN;
        }

        // PLACE_CODE聚合时，所有case的place_code名称相同，只取第一个，且加粗显示
        if (CollectionUtils.isNotEmpty(aggregateBy) && aggregateBy.contains(AggregateFieldEnum.PLACE_CODE.getCode())) {
            String placeCode = placeCodeList.get(0);
            return StringUtils.equals(placeCode, CommonConstant.UNKNOWN) ? placeCode
                    : DxRichTextUtils.toBold(placeCode);
        }

        // 非聚合情况，显示所有placeCode名称
        return Joiner.on(", ").skipNulls().join(placeCodeList);
    }
} 