package com.sankuai.wallemonitor.risk.center.domain.process;


import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.VEHICLE_RUNTIME_INFO_CONTEXT_UPDATED_PROCESSOR_ENTRY;

import com.google.common.collect.Lists;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetectorManager;
import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetectorManager.DetectorParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.VehicleRuntimeInfoParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 车辆运行时数据更新异步处理器
 * 1、触发风险检测器执行风险检测
 */
@Slf4j
@Component
public class VehicleRuntimeInfoContextUpdatedProcess implements DomainEventProcess {

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository repository;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private RiskDetectorManager riskDetectorManager;

    @Override
    @ZebraForceMaster
    @OperateEnter(VEHICLE_RUNTIME_INFO_CONTEXT_UPDATED_PROCESSOR_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != VehicleRuntimeInfoContextDO.class) {
            return true;
        }

        DomainEventChangeDTO<VehicleRuntimeInfoContextDO> typedDomainEvent = (DomainEventChangeDTO<VehicleRuntimeInfoContextDO>) eventDTO;
        List<VehicleRuntimeInfoContextDO> updatedVehicleList = typedDomainEvent.getBySingleField(
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "lastUpdateTime"));
        if (CollectionUtils.isEmpty(updatedVehicleList)) {
            return true;
        }

        // 这里是异步处理，所以不信任变更的实体的桩体
        List<String> vinList = updatedVehicleList.stream().map(VehicleRuntimeInfoContextDO::getVin)
                .collect(Collectors.toList());
        return handleUpdateMessage(vinList);
    }

    /**
     * 处理更新的消息
     *
     * @param vinList
     */
    private Boolean handleUpdateMessage(List<String> vinList) {
        Map<String, VehicleRuntimeInfoContextDO> vehicleRuntimeMap = repository.getFullByVin(vinList).stream()
                .collect(Collectors.toMap(VehicleRuntimeInfoContextDO::getVin, Function.identity(), (v1, v2) -> v1));
        Map<String, VehicleEveInfoVTO> vehicleEveInfoMap = vehicleAdapter
                .queryRuntimeVehicleInfo(VehicleRuntimeInfoParamVTO.builder().vinList(vinList).build()).stream()
                .collect(Collectors.toMap(VehicleEveInfoVTO::getVin, Function.identity(), (v1, v2) -> v1));

        // 通过Lion配置过滤掉一些不合法的车辆（如：H24车配置未完成上线运行 产生误报）
        List<String> validVinList = vehicleEveInfoMap.values().stream()
                .filter(eveInfo -> lionConfigRepository.isValidVehicle(eveInfo))
                .map(VehicleEveInfoVTO::getVin)
                .collect(Collectors.toList());
        logInvalidVinList(vinList, validVinList);

        Integer maxBatchSize = lionConfigRepository.getListPartitionBatchSize(
                "vehicleRuntimeInfoContextUpdatedProcess");
        Lists.partition(validVinList, maxBatchSize).forEach(subVinList -> {
            try {
                List<VehicleRuntimeInfoContextDO> runtimeInfoContextDOS = subVinList.stream()
                        .filter(vehicleRuntimeMap::containsKey).map(vehicleRuntimeMap::get)
                        .collect(Collectors.toList());
                List<VehicleEveInfoVTO> vehicleEveInfoVTOList = subVinList.stream().map(vehicleEveInfoMap::get)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                riskDetectorManager.process(DetectorParamDTO.builder().eveInfoList(vehicleEveInfoVTOList)
                        .vinRunTimeList(runtimeInfoContextDOS).build());
            } catch (Exception e) {
                log.error("handleUpdateMessage error, subList = {}", subVinList, e);
            }
        });
        return true;
    }

    /**
     * 打印无效的VIN列表
     *
     * @param vinList
     * @param validVinList
     * @return
     */
    private void logInvalidVinList(List<String> vinList, List<String> validVinList) {
        Set<String> validVinSet = new HashSet<>(validVinList);
        List<String> invalidVinList = vinList.stream()
                .filter(vin -> !validVinSet.contains(vin))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(invalidVinList)) {
            log.warn("invalid vin list: {}, denied to detect any type risk", invalidVinList);
        }
    }
}

