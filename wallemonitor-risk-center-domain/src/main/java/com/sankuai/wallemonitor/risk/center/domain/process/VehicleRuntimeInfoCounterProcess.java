package com.sankuai.wallemonitor.risk.center.domain.process;

import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.VEHICLE_RUNTIME_INFO_COUNTER_UPDATED_PROCESSOR_ENTRY;

import com.google.common.collect.Lists;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.VehicleCounterRuleConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.VehicleCounterRuleDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 车辆上下文检测器
 */
@Slf4j
@Component
public class VehicleRuntimeInfoCounterProcess implements DomainEventProcess {

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository repository;

    @Resource
    private LockUtils lockUtils;

    @Override
    @ZebraForceMaster
    @OperateEnter(VEHICLE_RUNTIME_INFO_COUNTER_UPDATED_PROCESSOR_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != VehicleRuntimeInfoContextDO.class) {
            return true;
        }
        DomainEventChangeDTO<VehicleRuntimeInfoContextDO> typedDomainEvent = (DomainEventChangeDTO<VehicleRuntimeInfoContextDO>)eventDTO;
        List<VehicleRuntimeInfoContextDO> updatedVehicleList = typedDomainEvent.getBySingleField(
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "lastUpdateTime"));
        if (CollectionUtils.isEmpty(updatedVehicleList)) {
            return true;
        }
        // 这里是异步处理，所以不信任变更的实体的桩体
        List<String> vinList = updatedVehicleList.stream().map(VehicleRuntimeInfoContextDO::getVin)
                .collect(Collectors.toList());
        return handleUpdateMessage(vinList, eventDTO.getTimestamp());
    }

    /**
     * 处理更新的消息
     *
     * @param vinList
     * @param timestamp
     */
    private Boolean handleUpdateMessage(List<String> vinList, Long timestamp) {
        if (CollectionUtils.isEmpty(vinList)) {
            return true;
        }
        VehicleCounterRuleConfig config = lionConfigRepository.getVehicleCounterRuleConfig();
        if (config == null || CollectionUtils.isEmpty(config.getCounterRuleList())) {
            return true;
        }
        List<VehicleRuntimeInfoContextDO> vehicleRuntimeInfoContextDOList = repository.getFullByVin(vinList);
        if (CollectionUtils.isEmpty(vehicleRuntimeInfoContextDOList)) {
            return true;
        }
        // 更新
        ParallelExecutor.executeParallelTasks("vehicle_runtime_info", vehicleRuntimeInfoContextDOList,
                (context) -> this.handleProcess(context, config, timestamp));
        // 开始对车辆进行加锁，然后计算
        return true;
    }

    /**
     * 处理一个车的counter
     *
     * @param vehicleRuntimeInfoContextDO
     * @param config
     */
    private void handleProcess(VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO,
            VehicleCounterRuleConfig config, Long timestamp) {
        try {
            // 更新计时
            lockUtils.batchLockCanWait(
                    LockKeyPreUtil.buildVinCounter(Lists.newArrayList(vehicleRuntimeInfoContextDO.getVin())), () -> {
                        // 对通用类型的做计算，包含表里面的和非表里面的
                        List<Pair<Boolean, VehicleCounterRuleDO>> counter = new ArrayList<>();
                        counter.addAll(config.doCounter(vehicleRuntimeInfoContextDO, false));
                        counter.addAll(config.doCounter(vehicleRuntimeInfoContextDO, true));
                        vehicleRuntimeInfoContextDO.updateCounterMap(counter);
                        // 保存counter信息
                        repository.updateCache(vehicleRuntimeInfoContextDO, timestamp);
                    });
        } catch (UnableGetLockException e) {
            log.warn("counter process error,no lock", e);
        } catch (Exception e) {
            log.error("handle process error", e);
        }

    }

}
