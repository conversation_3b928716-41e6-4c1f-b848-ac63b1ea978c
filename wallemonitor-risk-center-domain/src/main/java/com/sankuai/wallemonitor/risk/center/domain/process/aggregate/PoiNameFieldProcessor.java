package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.google.common.base.Joiner;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AggregateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.DxRichTextUtils;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * POI名称字段处理器
 */
@Component
public class PoiNameFieldProcessor implements TemplateFieldProcessor {

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {
        if (CollectionUtils.isEmpty(riskCaseList)) {
            return;
        }

        List<String> poiNameList = riskCaseList.stream().map(this::buildPoiName).collect(Collectors.toList());

        // 根据聚合字段进行优化
        String poiNameValue = optimizeForAggregation(poiNameList, aggregateBy);

        template.setPoiName(poiNameValue);
        template.setShowPoiName(true);
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.POI_NAME.getCode().equals(fieldCode);
    }

    /**
     * 构建POI名称
     */
    private String buildPoiName(RiskCaseDO caseDO) {
        return StringUtils.defaultIfBlank(caseDO.getPoiName(), CommonConstant.UNKNOWN);
    }

    /**
     * 根据聚合字段优化显示内容
     */
    private String optimizeForAggregation(List<String> poiNameList, List<String> aggregateBy) {
        if (CollectionUtils.isEmpty(poiNameList)) {
            return CommonConstant.UNKNOWN;
        }

        // POI聚合时，所有case的POI名称相同，只取第一个，且加粗显示
        // 按照泊位聚合时，也只取一个
        if (CollectionUtils.isNotEmpty(aggregateBy) && (aggregateBy.contains(AggregateFieldEnum.POI_NAME.getCode())
                || aggregateBy.contains(AggregateFieldEnum.PARKING_PLOT_ID.getCode()))) {
            String poiName = poiNameList.get(0);
            return StringUtils.equals(CommonConstant.UNKNOWN, poiName) ? poiName : DxRichTextUtils.toBold(poiName);
        }

        // 非聚合情况，显示所有POI名称
        return Joiner.on(", ").skipNulls().join(poiNameList);
    }
} 