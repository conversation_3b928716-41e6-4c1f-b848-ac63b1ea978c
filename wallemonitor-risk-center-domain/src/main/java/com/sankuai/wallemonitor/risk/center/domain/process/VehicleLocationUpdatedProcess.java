package com.sankuai.wallemonitor.risk.center.domain.process;

import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.VEHICLE_LOCATION_UPDATED_PROCESSOR_ENTRY;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeLocationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoLocationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.VehicleRuntimeLocationDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 车辆位置实时更新
 */
@Slf4j
@Component
public class VehicleLocationUpdatedProcess implements DomainEventProcess {

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleRuntimeInfoLocationRepository locationRepository;

    @Resource
    private LockUtils lockUtils;

    @Override
    @ZebraForceMaster
    @OperateEnter(VEHICLE_LOCATION_UPDATED_PROCESSOR_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != VehicleRuntimeInfoContextDO.class) {
            return true;
        }
        DomainEventChangeDTO<VehicleRuntimeInfoContextDO> typedDomainEvent = (DomainEventChangeDTO<VehicleRuntimeInfoContextDO>)eventDTO;
        List<VehicleRuntimeInfoContextDO> updatedVehicleList = typedDomainEvent.getBySingleField(
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "lastUpdateTime"));
        if (CollectionUtils.isEmpty(updatedVehicleList)) {
            return true;
        }
        return handleUpdateMessage(updatedVehicleList);
    }

    /**
     * 处理更新的消息
     *
     * @param runtimeInfoList
     */
    private Boolean handleUpdateMessage(List<VehicleRuntimeInfoContextDO> runtimeInfoList) {
        List<String> vinList = runtimeInfoList.stream().map(VehicleRuntimeInfoContextDO::getVin)
                .collect(Collectors.toList());
        Map<String, VehicleRuntimeInfoContextDO> vin2RuntimeInfo = runtimeInfoList.stream()
                .collect(Collectors.toMap(VehicleRuntimeInfoContextDO::getVin, Function.identity(), (v1, v2) -> v1));
        Map<String, VehicleRuntimeLocationDO> vin2Location = locationRepository
                .queryByParam(VehicleRuntimeLocationDOQueryParamDTO.builder().vinList(vinList).build()).stream()
                .collect(Collectors.toMap(VehicleRuntimeLocationDO::getVin, Function.identity(), (v1, v2) -> v1));

        lockUtils.batchLockCanWait(LockKeyPreUtil.buildLocationUpdateKey(vinList), () -> {
            try {
                vinList.forEach(vin -> {
                    VehicleRuntimeInfoContextDO contextDO = vin2RuntimeInfo.get(vin);
                    if (Objects.isNull(contextDO) || Objects.isNull(contextDO.getLocation())
                            || contextDO.getLocation().invalid()) {
                        return;
                    }
                    // 取数
                    VehicleRuntimeLocationDO vinLocation = vin2Location.computeIfAbsent(vin,
                            vinKey -> VehicleRuntimeLocationDO.builder().vin(vinKey).build());
                    vinLocation.setLocation(contextDO.getLocation());
                    // 保存
                    locationRepository.save(vinLocation);
                });
            } catch (Exception e) {
                log.error("handleUpdateMessage error ", e);
            }
        });
        return true;
    }

}
