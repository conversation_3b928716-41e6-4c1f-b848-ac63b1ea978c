package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.UserInfoRepository;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 安全员字段处理器
 * 处理安全员信息的显示逻辑
 */
@Component
@Slf4j
public class SecurityOfficerFieldProcessor implements TemplateFieldProcessor {

    @Resource
    private UserInfoRepository userInfoRepository;

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {

        if (CollectionUtils.isEmpty(riskCaseList)) {
            template.setShowSecurityOfficer(true);
            template.setShowMster(false);
            log.info("SecurityOfficerFieldProcessor.processField 1");
            template.setSecurityOfficer("没有配置安全员");
            return;
        }

        try {
            // 从context中获取车辆信息
            if (context == null) {
                template.setShowSecurityOfficer(true);
                template.setShowMster(false);
                log.info("SecurityOfficerFieldProcessor.processField 2");
                template.setSecurityOfficer("没有配置安全员");
                return;
            }

            // 收集所有VIN
            List<String> vinList = new ArrayList<>();
            for (RiskCaseDO riskCase : riskCaseList) {
                VehicleInfoDO vehicleInfo = context.getVehicleInfo(riskCase.getCaseId());
                if (vehicleInfo != null && vehicleInfo.getVin() != null) {
                    vinList.add(vehicleInfo.getVin());
                }
            }

            if (CollectionUtils.isEmpty(vinList)) {
                template.setShowSecurityOfficer(true);
                template.setShowMster(false);
                log.info("SecurityOfficerFieldProcessor.processField 3");
                template.setSecurityOfficer("没有配置安全员");
                return;
            }

            // 1. VIN -> MIS
            List<String> misList = userInfoRepository.batchGetSubstituteByVin(vinList);
      

            if (CollectionUtils.isEmpty(misList)) {
                template.setShowSecurityOfficer(true);
                template.setShowMster(false);
                log.info("SecurityOfficerFieldProcessor.processField 4");
                template.setSecurityOfficer("没有配置安全员");
                return;
            }

            // 2. MIS -> 姓名
            List<String> nameList = userInfoRepository.batchGetNamesByMis(misList);

            // 3. MIS -> UID
            List<Long> uidList = userInfoRepository.batchGetUidsByMis(misList);

            // 4. 构建@人格式
            List<String> atPersonList = buildAtPersonList(misList, nameList, uidList);

            // 5. 拼接结果（多个安全员用空格分隔）
            String securityOfficerText = String.join(" ", atPersonList);

            template.setSecurityOfficer(securityOfficerText);
            template.setShowSecurityOfficer(true);
            template.setShowMster(false);

        } catch (Exception e) {
            log.error("处理安全员字段失败", e);
            template.setShowSecurityOfficer(true);
            template.setShowMster(false);
            template.setSecurityOfficer("没有配置安全员");
        }
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.SECURITY_OFFICER.getCode().equals(fieldCode);
    }
 

    /**
     * 构建@人格式列表
     */
    private List<String> buildAtPersonList(List<String> misList, List<String> nameList, List<Long> uidList) {
        List<String> result = new ArrayList<>();

        for (int i = 0; i < misList.size(); i++) {
            String mis = misList.get(i);
            String name = i < nameList.size() ? nameList.get(i) : mis;
            Long uid = i < uidList.size() ? uidList.get(i) : null;

            // 构建@人格式
            String uidParam = uid != null ? uid.toString() : "";
            String profileUrl = String.format("mtdaxiang://www.meituan.com/profile?uid=%s&isAt=true", uidParam);
            String atPersonFormat = String.format("[@%s|%s]", name, profileUrl);

            result.add(atPersonFormat);
        }

        return result;
    }
}
