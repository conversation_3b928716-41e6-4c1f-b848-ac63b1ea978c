package com.sankuai.wallemonitor.risk.center.domain.param;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarkSendMessageParamDTO {
    private RiskCheckingQueueItemDO queueItem;
    private Long checkIntervalSeconds;
    private boolean isDynamic;
    private boolean withSpeed;
    private String version;

}
