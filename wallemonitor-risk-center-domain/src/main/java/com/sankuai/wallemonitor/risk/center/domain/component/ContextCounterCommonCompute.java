package com.sankuai.wallemonitor.risk.center.domain.component;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter.SearchNearbyRequestVTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleInQueuePositionDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapElementTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO.VehicleObstacleContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ContextCounterCommonCompute {

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private VehicleAdapter vehicleAdapter;


    /**
     * 计算等待时刻，前方是否有行人通行
     * 
     * @param contextDO
     * @param maxAngle
     * @param searchDistance
     * @param nearestDistance
     * @param obsFinTypeList
     * @param preDistance
     * @param pastSecond
     * @return
     */
    public boolean isWalkerWaitInCrossWalk(VehicleRuntimeInfoContextDO contextDO, double maxAngle,
            double searchDistance, double nearestDistance, List<String> obsFinTypeList, double preDistance,
            int pastSecond) {
        if (contextDO == null || contextDO.getStagnationCounter() == null
                || contextDO.getStagnationCounter().isCountFinished()) {
            // 如果不停，那么就不计时
            log.info("not in stagnation counter ");
            return false;
        }
        // 取停滞开始的时间
        Date occurTime = contextDO.getStagnationCounter().getStartTime();
        // 前序点
        VehicleInQueuePositionDTO prePosition = getPreVehiclePosition(occurTime, preDistance, pastSecond, contextDO);
        if (prePosition == null) {
            log.info("pre position is null");
            return false;
        }
        PositionDO preLocation = prePosition.getPosition();
        PositionDO curLocation = contextDO.getLocation();
        // 区域
        String area = vehicleAdapter.getVehicleHdMapArea(contextDO.getVin());
        // 点
        List<PositionDO> refinedLineList = contextDO.getRefinedLineList();
        // 找人行横道
        List<HdMapElementGeoDO> crossWalkList = hdMapAdapter.searchNearby(SearchNearbyRequestVTO.builder()
                        // 区域
                        .area(area)
                        // 定位
                        .positionDO(contextDO.getLocation())
                        .restrictType(Lists.newArrayList(HdMapElementTypeEnum.CROSSWALK.name()))
                        // 检索距离 (最大不超过20m)
                        .range(searchDistance)
                        // 地图类型
                        .hdMapEnum(HdMapElementTypeEnum.CROSSWALK.getMapValue()).build())
                .stream()
                // 必须在refine关联的线上
                .filter(walkGeo -> CollectionUtils.isNotEmpty(refinedLineList) && refinedLineList.stream()
                        .anyMatch(walkGeo::isInPolygon)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crossWalkList)) {
            log.info("crossWalkList is empty");
            return false;
        }
        // 检索出来全部的人行横道id
        List<String> crossWalkIdList = crossWalkList.stream().map(HdMapElementGeoDO::getId)
                .collect(Collectors.toList());
        // 压着的人行横道
        List<String> inWalkCrossWalkList = new ArrayList<>();
        // 距离且夹角满足
        List<String> nextNearCrossWalkList = new ArrayList<>();
        // 不满足的List
        List<String> notSatisfiedCrossWalkList = new ArrayList<>();
        // 找最近距离满足要求的
        HdMapElementGeoDO nearestCrossWalk = crossWalkList.stream().filter(crossWalk -> {
            PolygonDO polygonDO = crossWalk.getPolygonDO();
            if (polygonDO == null) {
                notSatisfiedCrossWalkList.add(crossWalk.getId());
                return false;
            }
            if (polygonDO.isInPolygon(contextDO.getLocation())) {
                // 如果就在人行横道上，这种也不算
                inWalkCrossWalkList.add(crossWalk.getId());
                return false;
            }
            // 计算距离
            Pair<Double, Pair<PositionDO, PositionDO>> nearestPair = GeoToolsUtil
                    .pointToLineSegmentDistanceWithPoint(curLocation, polygonDO.getPoints());
            if (nearestPair == null || nearestPair.getKey() == null) {
                notSatisfiedCrossWalkList.add(crossWalk.getId());
                return false;
            }
            // 最近的两个人行横道的点
            Pair<PositionDO, PositionDO> lineSegment = nearestPair.getValue();
            // 取任意一个
            PositionDO crossWalkOnePoint = lineSegment.getKey();
            PositionDO crossWalkTwoPoint = lineSegment.getValue();
            if (crossWalkOnePoint == null) {
                notSatisfiedCrossWalkList.add(crossWalk.getId());
                return false;
            }
            List<Double> angleList = new ArrayList<>();
            // 【前序 -> 当前 】 和 【当前->人行道任一点】，满足夹角小于逻辑
            Double oneAngle = GeoToolsUtil.angleCalc(preLocation, curLocation, curLocation, crossWalkOnePoint);
            Double twoAngle = GeoToolsUtil.angleCalc(preLocation, curLocation, curLocation, crossWalkTwoPoint);
            angleList.add(oneAngle);
            angleList.add(twoAngle);
            // 必须都小于
            boolean matchedAngle = angleList.stream().allMatch(angle -> angle != null && angle <= maxAngle);
            // 距离满足
            return nearestPair.getKey() <= nearestDistance && matchedAngle;
        }).peek(crossWalk -> nextNearCrossWalkList.add(crossWalk.getId())).findFirst().orElse(null);
        // 记录日志，输出最近的人行横道、不满足条件的人行横道以及正在人行横道上的人行横道信息。
        log.info(
                "crossWalkIdList:{},nearestCrossWalk:{},notSatisfiedCrossWalkList:{},inWalkCrossWalkList:{},nextNearCrossWalkList:{}",
                crossWalkIdList, nearestCrossWalk, notSatisfiedCrossWalkList, inWalkCrossWalkList,
                nextNearCrossWalkList);
        if (nearestCrossWalk == null) {
            log.warn("nearestCrossWalk is null");
            return false;
        }
        // 有路口，找障碍物
        List<String> obstacleIdList = inCrossWalkObstacleList(contextDO, area, nearestCrossWalk, obsFinTypeList);
        // 接下来找
        log.info("in obstacleIdList:{}", obstacleIdList);
        // 确实包含
        return CollectionUtils.isNotEmpty(obstacleIdList);
    }

    /**
     * 获取
     *
     * @param contextDO
     * @param area
     * @param nearestCrossWalk
     * @param obsFinTypeList
     * @return
     */
    private List<String> inCrossWalkObstacleList(VehicleRuntimeInfoContextDO contextDO,
            String area, HdMapElementGeoDO nearestCrossWalk, List<String> obsFinTypeList) {
        if (nearestCrossWalk == null) {
            return new ArrayList<>();
        }
        List<PerceptionObstacle> obstacleAbstracts = Optional.ofNullable(contextDO.getObstacleContext())
                .map(VehicleObstacleContextDO::getPerceptionObstacle).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(obstacleAbstracts)) {
            return new ArrayList<>();
        }
        PolygonDO polygonDO = nearestCrossWalk.getPolygonDO();
        if (polygonDO == null) {
            return new ArrayList<>();
        }
        // 返回满足的
        return obstacleAbstracts.stream().filter(obstacle -> {
            if (!obsFinTypeList.contains(obstacle.findShortFineType())) {
                // 符合类型的要求
                return false;
            }
            // 获取zone
            Integer zone = hdMapAdapter.getUtmZoneByArea(area);
            // 获取定位
            PositionDO positionDO = GeoToolsUtil.utmToWgs84WithZone(obstacle.getPosition().getX(),
                    obstacle.getPosition().getY(), zone);
            // 判定
            return polygonDO.isInPolygon(positionDO);
        }).map(PerceptionObstacle::getId).collect(Collectors.toList());
    }

    /**
     * 获得前序点
     * 
     * @param preDistance
     * @param pastSecond
     * @param runtimeContext
     * @return
     */
    private VehicleInQueuePositionDTO getPreVehiclePosition(Date occurTime, double preDistance, int pastSecond,
            VehicleRuntimeInfoContextDO runtimeContext) {
        PositionDO curPosition = runtimeContext.getLocation();
        Date timeBeforeOccurTime = DatetimeUtil.getNSecondsBeforeDateTime(occurTime, pastSecond);
        // 查询过去的两个点
        List<VehicleInQueuePositionDTO> vehicleLastLocationList = vehicleAdapter
                .queryVehicleHistoryDataFromEveReplay(runtimeContext.getVin(),
                        DatetimeUtil.getTimeInSeconds(timeBeforeOccurTime), DatetimeUtil.getTimeInSeconds(occurTime))
                .stream().map(vehicleDataInfoVO -> {
                    PositionDO positionDO = PositionDO.getPosition(vehicleDataInfoVO.getLongitude(),
                            vehicleDataInfoVO.getLatitude(), CoordinateSystemEnum.WGS84);
                    return VehicleInQueuePositionDTO.builder().position(positionDO)
                            .distance(GeoToolsUtil.distance(positionDO, curPosition))     // 计算距离
                            .time(vehicleDataInfoVO.getTime())  // 时间
                            .build();
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehicleLastLocationList)) {
            log.warn("无法找到车辆历史定位信息");
            return null;
        }

        // 保存过去历史定位点
        runtimeContext.setPrePositionList(new ArrayList<>(vehicleLastLocationList));
        Collections.reverse(vehicleLastLocationList);   // 直接倒序
        // 取最后一个不靠近定位点的点，防止有拐弯的情况
        return IntStream.range(0, vehicleLastLocationList.size()).filter(index -> {
            VehicleInQueuePositionDTO position = vehicleLastLocationList.get(index);
            // 满足最小距离，防止过近引发异常
            return Objects.nonNull(position) && !position.invalid() // 定位合法
                    && Objects.nonNull(position.getDistance()) // 有距离
                    && position.getDistance() > preDistance;
        }).mapToObj(vehicleLastLocationList::get).findFirst().orElseGet(() -> vehicleLastLocationList.get(0));
    }

}
