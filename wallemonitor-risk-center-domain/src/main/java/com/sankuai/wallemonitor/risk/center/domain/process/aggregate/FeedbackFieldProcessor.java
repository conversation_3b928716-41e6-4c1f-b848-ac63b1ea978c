package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordLabelEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 反馈字段处理器
 * 处理大象卡片的反馈相关字段
 */
@Component
@Slf4j
public class FeedbackFieldProcessor implements TemplateFieldProcessor {

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {
        template.setShowFeedBack(true);
        template.setFeedback(AlertRecordLabelEnum.UNLABELED.getVoteType());
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.FEEDBACK.getCode().equals(fieldCode);
    }
} 