package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.google.common.base.Joiner;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 车辆名称V2字段处理器
 * 处理车辆名称V2字段的显示逻辑
 */
@Component
@Slf4j
public class VehicleNameV2FieldProcessor implements TemplateFieldProcessor {

    // 风险案例详情URL模板
    private static final String RISK_CASE_DETAIL_URL_TEMPLATE = "https://eve.meituan.com/fe-panel-risk/index.html#/risk/caseList?openCaseId=%s";

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {
        if (CollectionUtils.isEmpty(riskCaseList)) {
            return;
        }

        List<String> vehicleNameAndIdList = riskCaseList.stream()
                .map(caseDO -> buildVehicleNameAndId(caseDO, context))
                .collect(Collectors.toList());

        String vehicleNameValue = Joiner.on("\n").skipNulls().join(vehicleNameAndIdList);

        template.setVehicleNameV2(vehicleNameValue);
        template.setShowVehicleNameV2(true);
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.VEHICLE_NAME_V2.getCode().equals(fieldCode);
    }

    /**
     * 构建车辆名称和ID的显示文本
     * 格式：[车辆号|风险案例详情URL]
     */
    private String buildVehicleNameAndId(RiskCaseDO caseDO, AggregateAlertContext context) {
        if (caseDO == null) {
            return CommonConstant.UNKNOWN;
        }

        VehicleInfoDO vehicleInfo = context.getVehicleInfo(caseDO.getCaseId());
        if (vehicleInfo == null) {
            return CommonConstant.UNKNOWN;
        }

        String vehicleName = vehicleInfo.getVehicleName();
        if (StringUtils.isBlank(vehicleName)) {
            vehicleName = CommonConstant.UNKNOWN;
        }

        // 构建风险案例详情URL
        String riskCaseDetailUrl = buildRiskCaseDetailUrl(caseDO);

        // 构建格式：[车辆号|URL]
        return String.format("[%s|%s]", vehicleName, riskCaseDetailUrl);
    }

    /**
     * 构建风险案例详情URL
     */
    private String buildRiskCaseDetailUrl(RiskCaseDO caseDO) {
        if (caseDO == null || StringUtils.isBlank(caseDO.getCaseId())) {
            return "";
        }

        try {
            return String.format(RISK_CASE_DETAIL_URL_TEMPLATE, caseDO.getCaseId());
        } catch (Exception e) {
            log.error("构建风险案例详情URL失败: caseId={}", caseDO.getCaseId(), e);
            return "";
        }
    }
}
