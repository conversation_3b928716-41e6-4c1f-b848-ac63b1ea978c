package com.sankuai.wallemonitor.risk.center.domain.config;

import com.sankuai.meituan.org.opensdk.client.RemoteServiceClient;

import com.sankuai.meituan.org.opensdk.service.EmpService;
import com.sankuai.meituan.org.opensdk.service.impl.EmpServiceImpl;
import com.sankuai.meituan.org.queryservice.domain.param.DataScope;
import com.sankuai.xm.udb.thrift.UdbOpenThriftBeans;
import com.sankuai.xm.udb.thrift.UdbOpenThriftClient;

import java.util.Arrays;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 组织架构服务配置类
 * 用于配置EmpService等组织架构相关的Bean
 *
 * <AUTHOR>
 * @date 2025/1/11
 */
@Configuration
public class OrgConfig {
    @Value("${org.remoteAppKey}")
    private String REMOTE_APP_KEY;

    @Value("${org.ak}")
    private String CLIENT_AK;
   
    @Value("${org.secret}")
    private String APP_SECRET;

    @Value("${org.tenantId}")
    private Integer APP_TENANT_ID;

    @Value("${org.source}")
    private String APP_SOURCE;


    @Bean
    public RemoteServiceClient remoteServiceClient() throws Exception {

        // 设置App默认的数据访问范围。如下设置，App默认所有的请求是针对美团租户下“MT”数据域的ORG数据
        DataScope dataScope = new DataScope();
        dataScope.setTenantId(APP_TENANT_ID);
        dataScope.setSources(Arrays.asList(APP_SOURCE));

        RemoteServiceClient remoteServiceClient = new RemoteServiceClient(CLIENT_AK, APP_SECRET, REMOTE_APP_KEY, dataScope);
        return remoteServiceClient;
    }

    /**
     * 配置EmpService Bean
     * 用于获取员工信息等组织架构服务
     *
     * @return EmpService实例
     */
    @Bean
    public EmpService empService(RemoteServiceClient remoteServiceClient) {
        EmpServiceImpl empService = new EmpServiceImpl(remoteServiceClient);
        return empService;
    }

    @Bean
    public UdbOpenThriftClient getUdbService() throws Exception {
        UdbOpenThriftClient udbClient = UdbOpenThriftBeans.get(UdbOpenThriftClient.class);
        return udbClient;
    }

}
