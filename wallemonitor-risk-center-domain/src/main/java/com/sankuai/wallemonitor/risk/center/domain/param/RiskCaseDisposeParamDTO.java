package com.sankuai.wallemonitor.risk.center.domain.param;

import com.sankuai.wallemonitor.risk.center.infra.enums.MrmRoleEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseVehicleStatusEnum;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCaseDisposeParamDTO {


    /**
     * traceId
     */
    private String traceId;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 处置角色
     */
    private MrmRoleEnum roleEnum;

    /**
     * 坐席mis号
     */
    private String mrmSeatMisId;
    /**
     * 坐席mis号
     */
    private String mrmSeatNo;

    /**
     * 坐席处置状态
     */
    private RiskCaseVehicleStatusEnum status;


    /**
     * 请求坐席时间
     */
    private Date requestSeatTime;

    /**
     * 分配坐席时间
     */
    private Date dispatchTime;

    /**
     * 连入时间
     */
    private Date seatConnectTime;

    /**
     * 退控时间
     */
    private Date seatExitTime;

}
