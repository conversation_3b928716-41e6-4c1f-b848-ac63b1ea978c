package com.sankuai.wallemonitor.risk.center.domain.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.xframe.config.annotation.ConfigValueListener;
import com.meituan.xframe.config.vo.ConfigChangedEvent;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.impl.MultiMarkMessageConsumeService;
import com.sankuai.wallemonitor.risk.center.infra.comsumprocess.proxy.CommonMessageConsumerProxy;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.MafkaMessageConsumerConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.MafkaMessageProducerConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.MarkMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.factory.MafkaFactory;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;

@Configuration
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Slf4j
public class MultiMarkMessageConfigDTO {

    /**
     * 命名空间
     */
    private static final String NAMESPACE = "waimai";

    /**
     * topic模板
     */
    private static final String TOPIC_TEMPLATE = "multi.auto.mark.message.%s";

    /**
     * consumer模板
     */
    private static final String CONSUMER_TEMPLATE = "multi.auto.mark.message.consumer.%s";

    @Resource
    private MultiMarkMessageConsumeService multiMarkMessageConsumeService;

    /**
     * 消息生产者
     */
    private Map<String, CommonMessageProducer<MarkMessageDTO>> markMessageProducer = new HashMap<>();

    /**
     * 消息生产者
     */
    private Map<String, CommonMessageConsumerProxy> markMessageConsumer = new HashMap<>();

    /**
     * 根据版本注册
     */
    @ConfigValueListener(key = LionKeyConstant.LION_KEY_MULTI_VERSION, executeOnStart = true)
    public void listener(ConfigChangedEvent configEvent) {
        String multiVersionListStr = configEvent.getValue();
        if (StringUtils.isBlank(multiVersionListStr)) {
            return;
        }
        Set<String> mafkaVersionList = JacksonUtils.from(multiVersionListStr, new TypeReference<Set<String>>() {});
        // 新增只新增，不删除
        if (CollectionUtils.isEmpty(mafkaVersionList)) {
            return;
        }
        // 默认的producer/consumer
        mafkaVersionList.add(CommonConstant.DEFAULT);
        mafkaVersionList.forEach(version -> {
            markMessageProducer.computeIfAbsent(version, this::buildProducer);
            markMessageConsumer.computeIfAbsent(version, this::buildConsumer);
        });

    }

    /**
     * 构建消息
     * 
     * @param version
     * @return
     */
    public CommonMessageProducer<MarkMessageDTO> buildProducer(String version) {
        if (StringUtils.isBlank(version)) {
            // 主标注链路，不在这里处理
            return null;
        }
        MafkaMessageProducerConfigDTO configDTO = MafkaMessageProducerConfigDTO.builder()
                //
                .appKey(AppKeyConstant.RISK_CENTER_APP_KEY)
                //
                .nameSpace(NAMESPACE)
                //
                .topicName(String.format(TOPIC_TEMPLATE, version)).build();
        // 注册
        try {
            return MafkaFactory.getMafkaCommonProducer(configDTO);
        } catch (Exception e) {
            log.error("buildProducer error:" + version, e);
            return null;
        }

    }

    /**
     * 构建消息
     *
     * @param mafkaVersionSuffix
     * @return
     */
    public CommonMessageConsumerProxy buildConsumer(String mafkaVersionSuffix) {
        if (StringUtils.isBlank(mafkaVersionSuffix)) {
            // 主标注链路，不在这里处理
            return null;
        }
        MafkaMessageConsumerConfigDTO configDTO = MafkaMessageConsumerConfigDTO.builder()
                //
                .appKey(AppKeyConstant.RISK_CENTER_APP_KEY)
                // 统一
                .nameSpace(NAMESPACE)
                // 需要拼接后缀
                .group(String.format(CONSUMER_TEMPLATE, mafkaVersionSuffix))
                // 需要拼接后缀
                .topicName(String.format(TOPIC_TEMPLATE, mafkaVersionSuffix))
                // 死信
                .deadLetter(true)
                // 延时
                .deadLetterDelayMills(6 * 1000L).build();
        try {
            // 注册
            return MafkaFactory.getMafkaCommonConsumer(configDTO, multiMarkMessageConsumeService);
        } catch (Exception e) {
            log.error("handleConsumer error:" + mafkaVersionSuffix, e);
            return null;
        }
    }

    public CommonMessageProducer<MarkMessageDTO> getMarkMessageProducerByVersion(String version) {
        if (StringUtils.isBlank(version)) {
            // 必须有版本
            return null;
        }
        // 如果版本没有配置默认的，使用 default的收发消息链路
        return markMessageProducer.getOrDefault(version, markMessageProducer.get(CommonConstant.DEFAULT));
    }
}
