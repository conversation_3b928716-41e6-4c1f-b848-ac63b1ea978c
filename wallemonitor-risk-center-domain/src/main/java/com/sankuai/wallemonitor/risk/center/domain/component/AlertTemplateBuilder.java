package com.sankuai.wallemonitor.risk.center.domain.component;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.aggregate.TemplateFieldProcessor;
import com.sankuai.wallemonitor.risk.center.domain.process.aggregate.TemplateFieldProcessorFactory;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.upgrade.AlertUpgradeStrategyDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ThemeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 告警模板构建器
 */
@Component
@Slf4j
public class AlertTemplateBuilder {

    @Resource
    private TemplateFieldProcessorFactory fieldProcessorFactory;

    /**
     * 构建告警模板
     *
     * @param strategyConfig 策略配置
     * @param riskCaseList   风险事件列表
     * @param context        聚合告警上下文
     * @return 大象卡片模板
     */
    public DxCardTemplateDO buildTemplate(AggregateStrategyConfigDTO strategyConfig, List<RiskCaseDO> riskCaseList,
            AggregateAlertContext context) {

        if (CollectionUtils.isEmpty(riskCaseList) || strategyConfig == null) {
            log.warn("构建模板参数为空");
            return null;
        }

        AlertTemplateConfigDTO alertTemplate = strategyConfig.getAlertTemplate();
        if (alertTemplate == null) {
            log.warn("告警模板配置为空");
            return null;
        }

        try {
            // 1. 初始化基础模板
            DxCardTemplateDO template = initializeBaseTemplate(alertTemplate, riskCaseList, context);

            // 2. 处理配置字段
            processConfiguredFields(template, alertTemplate, riskCaseList, context, strategyConfig.getAggregateBy());

            return template;
        } catch (Exception e) {
            log.error("构建告警模板失败", e);
            return null;
        }
    }

    /**
     * 初始化基础模板
     */
    private DxCardTemplateDO initializeBaseTemplate(AlertTemplateConfigDTO alertTemplate, List<RiskCaseDO> riskCaseList,
            AggregateAlertContext context) {
        DxCardTemplateDO template = new DxCardTemplateDO();

        // 构建标题
        String title = buildTemplateTitle(alertTemplate, riskCaseList, context);
        template.setTitle(title);

        // 设置主题色
        template.setThemeColor(ThemeEnum.RED.getValue());

        return template;
    }

    /**
     * 构建模板标题
     */
    private String buildTemplateTitle(AlertTemplateConfigDTO alertTemplate, List<RiskCaseDO> riskCaseList,
            AggregateAlertContext context) {

        List<String> vehicleNames = riskCaseList.stream()
                .map(caseDO ->
                        Optional.ofNullable(context.getVehicleInfo(caseDO.getCaseId()))
                                .map(VehicleInfoDO::getVehicleName).orElse(null)
                )
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehicleNames)) {
            return alertTemplate.getTitle();
        }
        String prefix = String.format("%s%s车辆", vehicleNames.get(0), vehicleNames.size() > 1 ? "等" : "");
        return prefix + alertTemplate.getTitle();
    }

    /**
     * 处理配置的字段
     */
    private void processConfiguredFields(DxCardTemplateDO template, AlertTemplateConfigDTO alertTemplate,
            List<RiskCaseDO> riskCaseList, AggregateAlertContext context, List<String> aggregateBy) {

        List<String> showFieldList = alertTemplate.getShowFieldList();
        log.info("卡片展示字段: {}", JacksonUtils.to(showFieldList));
        if (CollectionUtils.isEmpty(showFieldList)) {
            return;
        }

        // 使用字段处理器工厂处理每个字段
        for (String fieldCode : showFieldList) {
            TemplateFieldProcessor processor = fieldProcessorFactory.getProcessor(fieldCode);
            if (processor == null) {
                log.warn("未找到字段处理器: fieldCode={}", fieldCode);
                continue;
            }
            try {
                processor.processField(template, riskCaseList, context, alertTemplate, aggregateBy);
            } catch (Exception e) {
                log.error("处理字段失败: fieldCode={}", fieldCode, e);
            }
        }
    }

    /**
     * 基于升级策略配置构建升级告警模板
     * 完全复用现有的私有函数，只是增强为升级样式
     *
     * @param upgradeStrategy 升级策略配置
     * @param riskCaseList    风险事件列表
     * @param context         聚合告警上下文
     * @return 升级告警卡片模板
     */
    public DxCardTemplateDO buildUpgradeTemplate(AlertUpgradeStrategyDTO upgradeStrategy,
                                                List<RiskCaseDO> riskCaseList,
                                                AggregateAlertContext context) {
        if (CollectionUtils.isEmpty(riskCaseList) || upgradeStrategy == null || !upgradeStrategy.isValid()) {
            log.warn("构建升级告警模板参数无效");
            return null;
        }

        AlertTemplateConfigDTO alertTemplate = upgradeStrategy.getAlertTemplate();
        if (alertTemplate == null || !alertTemplate.isValid()) {
            log.warn("升级模板配置无效");
            return null;
        }

        try {
            // 1. 复用现有的初始化基础模板方法
            DxCardTemplateDO template = initializeBaseTemplate(alertTemplate, riskCaseList, context);

            // 2. 增强为升级告警样式
            enhanceAsUpgradeTemplate(template, alertTemplate, riskCaseList, context);

            List<String> aggregateBy = new ArrayList<>();
            // 3. 复用现有的字段处理方法
            processConfiguredFields(template, alertTemplate, riskCaseList, context, aggregateBy);

            log.info("升级告警模板构建成功, templateId: {}, configName: {}, riskCaseCount: {}",
                    alertTemplate.getTemplateId(), upgradeStrategy.getConfigName(), riskCaseList.size());
            return template;

        } catch (Exception e) {
            log.error("构建升级告警模板失败, configName: {}", upgradeStrategy.getConfigName(), e);
            return null;
        }
    }

    /**
     * 增强为升级告警样式
     * 在现有模板基础上添加升级告警的特有样式
     */
    private void enhanceAsUpgradeTemplate(DxCardTemplateDO template,
                                        AlertTemplateConfigDTO alertTemplate,
                                        List<RiskCaseDO> riskCaseList,
                                        AggregateAlertContext context) {
        // 1. 修改标题为升级告警格式
        String originalTitle = template.getTitle();
        if (StringUtils.isNotBlank(originalTitle) && !originalTitle.startsWith("[升级告警]")) {
            template.setTitle("[升级告警] " + originalTitle);
        }

        // 2. 设置升级告警红色主题
        template.setThemeColor(ThemeEnum.RED.getValue());

        // 3. 确保按钮可用
        template.setIsBtnDisabled(false);
        template.setShowButton(true);
    }
}