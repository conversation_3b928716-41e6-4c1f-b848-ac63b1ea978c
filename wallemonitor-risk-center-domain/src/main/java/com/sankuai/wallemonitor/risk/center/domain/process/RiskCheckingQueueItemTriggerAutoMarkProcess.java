package com.sankuai.wallemonitor.risk.center.domain.process;


import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskMarkService;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.EntityKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskMarkRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.VehicleRuntimeInfoParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 处理预检队列项状态为「检查中」
 */
@Slf4j
@Component
public class RiskCheckingQueueItemTriggerAutoMarkProcess implements
        DomainEventProcess {

    @Resource
    private RiskMarkRepository riskCheckQueueRepository;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskMarkService riskMarkService;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private LockUtils lockUtils;


    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.RISK_AUTO_MARK_TRIGGER_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != RiskCheckingQueueItemDO.class) {
            return true;
        }

        DomainEventChangeDTO<RiskCheckingQueueItemDO> typedDomainEvent = (DomainEventChangeDTO<RiskCheckingQueueItemDO>) eventDTO;
        List<RiskCheckingQueueItemDO> initItemList = new ArrayList<>(typedDomainEvent.getBySingleField(
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "checking")
                        //必须是新创建
                        && Objects.equals(entityFieldChangeRecordDTO.getChangeType(),
                        EntityKeyConstant.ENTITY_EMPTY_TO_VALUE)));
        if (CollectionUtils.isEmpty(initItemList)) {
            return true;
        }
        log.info("triggerAutoMark: {}", JacksonUtils.to(initItemList));
        // 做消息发送
        return handleSendMarkMessage(initItemList);
    }

    /**
     * 发送标注消息
     *
     * @param itemList
     * @return
     */
    private boolean handleSendMarkMessage(List<RiskCheckingQueueItemDO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return true;
        }
        List<String> strandingCaseIdList = itemList.stream().map(RiskCheckingQueueItemDO::getEventId)
                .collect(Collectors.toList());
        // 查询停滞的case,停滞不当的eventId，就是停滞的caseId
        List<RiskCaseDO> strandingCaseList = riskCaseRepository
                .queryByParam(RiskCaseDOQueryParamDTO.builder().caseIdList(strandingCaseIdList).build());
        if (CollectionUtils.isEmpty(strandingCaseList)) {
            // 返回true
            return true;
        }
        Set<String> strandingEventIdList = strandingCaseList.stream().map(RiskCaseDO::getEventId)
                .collect(Collectors.toSet());
        Map<String, RiskAutoCheckConfigDTO> autoMarkConfigList = lionConfigRepository.getAllAutoMarkConfig();
        if (MapUtils.isEmpty(autoMarkConfigList)) {
            // 配置异常
            return true;
        }
        List<String> vinList = itemList.stream().map(RiskCheckingQueueItemDO::getVin).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vinList)) {
            return true;
        }
        // 取车辆的车型
        Map<String, VehicleEveInfoVTO> vin2VehicleEveInfoMap = vehicleAdapter
                .queryRuntimeVehicleInfo(VehicleRuntimeInfoParamVTO.builder().vinList(vinList).build()).stream()
                .collect(Collectors.toMap(VehicleEveInfoVTO::getVin, Function.identity(), (v1, v2) -> v1));
        // 每个配置一份数据 ，防止并发
        Map<String, List<RiskCheckingQueueItemDO>> markVersionItemMap = autoMarkConfigList.keySet().stream()
                .collect(Collectors.toMap(Function.identity(), (key) -> JacksonUtils.from(JacksonUtils.to(itemList),
                        new TypeReference<List<RiskCheckingQueueItemDO>>() {})));
        ParallelExecutor.executeParallelTasks("multiAutoMark", autoMarkConfigList, (version, autoMarkConfigDTO) -> {
            if (autoMarkConfigDTO == null) {
                return;
            }
            if (!autoMarkConfigDTO.isInMarkTime()) {
                // 不在时段内不进行处理
                return;
            }
            // 使用标注的对象
            List<RiskCheckingQueueItemDO> versionItemList = markVersionItemMap.get(version);
            if (CollectionUtils.isEmpty(versionItemList)) {
                return;
            }
            List<RiskCheckingQueueItemDO> needMarkList = versionItemList.stream().filter(checkingQueueItemDO -> {
                VehicleEveInfoVTO eveInfoVTO = vin2VehicleEveInfoMap.get(checkingQueueItemDO.getVin());
                return autoMarkConfigDTO.needToMark(eveInfoVTO, checkingQueueItemDO);

            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(needMarkList)) {
                return;
            }
            // 这里要触发标注
            lockUtils.batchLockCanWait(LockKeyPreUtil.buildMultiMarkWithEventId(strandingEventIdList, version), () -> {
                // 保存标注使用的对象
                riskCheckQueueRepository.saveMarkItem(needMarkList, version);
                // 触发标注
                riskMarkService.triggerAutoMark(needMarkList, version);
            });
        });
        return true;
    }


}
