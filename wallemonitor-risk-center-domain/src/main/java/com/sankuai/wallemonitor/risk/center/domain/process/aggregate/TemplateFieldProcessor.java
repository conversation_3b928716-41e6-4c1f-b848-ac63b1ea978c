package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import java.util.List;

/**
 * 模板字段处理器接口
 */
public interface TemplateFieldProcessor {

    /**
     * 处理字段数据
     *
     * @param template      模板对象
     * @param riskCaseList  风险事件列表
     * @param context       聚合告警上下文
     * @param alertTemplate 告警模板配置
     * @param aggregateBy   聚合字段列表
     */
    void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy);

    /**
     * 是否支持该字段类型
     *
     * @param fieldCode 字段编码
     * @return 是否支持
     */
    boolean supports(String fieldCode);
} 