package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
/**
 * 发生时间字段处理器
 */
@Component
public class OccurTimeFieldProcessor implements TemplateFieldProcessor {

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {
        if (CollectionUtils.isEmpty(riskCaseList)) {
            return;
        }
        List<String> occurTimeList = riskCaseList.stream()
                .map(caseDO -> caseDO.getOccurTime() != null ? DatetimeUtil.formatTimeWithRounding(
                        caseDO.getOccurTime())
                        : "未知时间").collect(Collectors.toList());

        String occurTimeStr = occurTimeList.size() > 1 ?
                occurTimeList.get(0) + " ~ " + occurTimeList.get(occurTimeList.size() - 1) : occurTimeList.get(0);
        template.setOccurTime(occurTimeStr);
        template.setShowOccurTime(true);
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.OCCUR_TIME.getCode().equals(fieldCode);
    }
} 