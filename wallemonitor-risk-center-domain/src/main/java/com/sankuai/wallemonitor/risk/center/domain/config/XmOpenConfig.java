package com.sankuai.wallemonitor.risk.center.domain.config;

import com.sankuai.dxenterprise.open.gateway.service.dx.api.DxService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/6/16
 */
@Configuration
public class XmOpenConfig {

    @Bean
    public DxService dxService() {
        return new DxService();
    }
}
