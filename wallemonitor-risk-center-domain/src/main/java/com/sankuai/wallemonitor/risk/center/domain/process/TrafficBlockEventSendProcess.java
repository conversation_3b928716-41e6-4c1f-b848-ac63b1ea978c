package com.sankuai.wallemonitor.risk.center.domain.process;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.domain.enums.MessageType;
import com.sankuai.wallemonitor.risk.center.domain.service.TrafficBlockEventService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.EntityKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficBlockEventSendDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskVehicleExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 堵路事件消息发送处理器
 * 监听RiskCaseDO的变化，当发生堵路事件相关的变化时，发送消息到MQ
 */
@Component
@Slf4j
public class TrafficBlockEventSendProcess implements DomainEventProcess {

    /**
     * 消息生产者
     */
    @MessageProducer(topic=MessageTopicEnum.WALLEMONITOR_RISK_CENTER_COMMON_OUTPUT_RISK_EVENT_MESSAGE)
    private CommonMessageProducer<TrafficBlockEventSendDTO> messageProducer;

    @Resource
    private TrafficBlockEventService trafficBlockEventService;


    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.RISK_EVENT_CONSUMER_ENTER_TRAFFIC_BLOCK_HANDLE)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {


        // 判断事件类型是否为RiskCaseVehicleRelationDO
        if (eventDTO.getDomainClass() != RiskCaseVehicleRelationDO.class) {
            return true;
        }

        // 类型转换
        DomainEventChangeDTO<RiskCaseVehicleRelationDO> typedDomainEvent = (DomainEventChangeDTO<RiskCaseVehicleRelationDO>) eventDTO;


        // 获取状态变更的事件
        List<RiskCaseVehicleRelationDO> riskCaseDOList = typedDomainEvent.getBySingleField(
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getChangeType(),
                        EntityKeyConstant.ENTITY_EMPTY_TO_VALUE)).stream().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return true;
        }

        // 处理事件
        return handleProcessMsg(riskCaseDOList);
    }

    /**
     * 处理消息
     *
     * @param riskCaseDOList 风险事件列表
     * @return 处理结果
     */
    public boolean handleProcessMsg(List<RiskCaseVehicleRelationDO> riskCaseDOList) {
        for (RiskCaseVehicleRelationDO relationDO : riskCaseDOList) {
            try {
                // 判断是否为堵路事件
                if (!isTrafficBlockEvent(relationDO)) {
                    continue;
                }

                // 发送堵路事件消息
                sendTrafficBlockEventMessage(relationDO);
            } catch (Exception e) {
                log.error("TrafficBlockEventSendProcess# handleProcessMsg error, riskCaseDO = {}", relationDO, e);
            }
        }
        return true;
    }

    /**
     * 判断是否为堵路事件
     *
     * @param relationDO 风险事件关联关系
     * @return 是否为堵路事件
     */
    private boolean isTrafficBlockEvent(RiskCaseVehicleRelationDO relationDO) {
        return Objects.equals(relationDO.getType(), RiskCaseTypeEnum.TRAFFIC_BLOCK_EVENT);
    }

    /**
     * 发送堵路事件消息
     *
     * @param relationDO 风险事件关联关系
     */
    private void sendTrafficBlockEventMessage(RiskCaseVehicleRelationDO relationDO) {
        RiskVehicleExtInfoDO extInfo = relationDO.getExtInfo();
        if (extInfo == null || extInfo.getVehicleContent() == null) {
            log.error("TrafficBlockEventSendProcess# sendTrafficBlockEventMessage, extInfo or vehicleContent is null, relationDO = {}", relationDO);
            return;
        }

        Map<String, Object> riskCaseMap = extInfo.getVehicleContent();


        TrafficBlockEventSendDTO sendDTO = TrafficBlockEventSendDTO.builder()
                .caseId((String) riskCaseMap.get("caseId"))
                .vin((String) riskCaseMap.get("vin"))
                .occurTime((String) riskCaseMap.get("occurTime"))
                .closeTime((String) riskCaseMap.get("closeTime"))
                .latitude((Double) riskCaseMap.get("latitude"))
                .longitude((Double) riskCaseMap.get("longitude"))
                .extInfo((Map<String, Object>) riskCaseMap.get("extInfo"))
                .build();

        // 发送消息
        String messageId = messageProducer.sendMqCommonMessage(sendDTO, MessageType.TRAFFIC_BLOCK_EVENT);
        log.info("TrafficBlockEventSendProcess# sendTrafficBlockEventMessage success, messageId = {}, caseId = {}",
                messageId, sendDTO.getCaseId());
    }


}