package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.google.common.base.Joiner;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.DxRichTextUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.UrlEncodeUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 车辆名称字段处理器
 */
@Component
public class VehicleNameFieldProcessor implements TemplateFieldProcessor {

    // 使用占位符的URL模板
    private static final String MONITOR_REPLAY_URL_TEMPLATE = "https://walle.meituan.com/app/monitor/v2?playType=backup&backupTime=%s&vin=%s";

    // 提前30秒的固定值
    private static final int TIME_ADVANCE_SECONDS = 30;

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {
        if (CollectionUtils.isEmpty(riskCaseList)) {
            return;
        }

        List<String> vehicleNameAndIdList = riskCaseList.stream()
                .map(caseDO -> buildVehicleNameAndId(caseDO, context))
                .collect(Collectors.toList());

        String vehicleNameValue = Joiner.on("\n").skipNulls().join(vehicleNameAndIdList);

        template.setVehicleName(vehicleNameValue);
        template.setShowVehicleName(true);
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.VEHICLE_NAME.getCode().equals(fieldCode);
    }

    /**
     * 构建车辆名称和ID，包含监控回放链接
     */
    private String buildVehicleNameAndId(RiskCaseDO caseDO, AggregateAlertContext context) {
        VehicleInfoDO vehicleInfo = context.getVehicleInfo(caseDO.getCaseId());
        if (vehicleInfo != null) {
            // 没有查到车名用vin填充
            String vehicleDisplayText =
                    StringUtils.isNotBlank(vehicleInfo.getVehicleName()) ? vehicleInfo.getVehicleName() + "/"
                            + vehicleInfo.getVehicleId() : vehicleInfo.getVin();
            // 生成监控回放链接
            String monitorUrl = buildMonitorUrl(vehicleInfo.getVin(), caseDO.getOccurTime());
            // 创建超链接
            return DxRichTextUtils.createLink(vehicleDisplayText, monitorUrl);
        }
        return CommonConstant.UNKNOWN;
    }

    /**
     * 构建监控回放URL
     *
     * @param vin       车辆VIN码
     * @param occurTime 事件发生时间
     * @return 监控回放URL
     */
    private String buildMonitorUrl(String vin, Date occurTime) {
        if (StringUtils.isBlank(vin) || occurTime == null) {
            return "";
        }
        // 计算提前30秒的时间
        Date advancedTime = DatetimeUtil.getBeforeTime(occurTime, TimeUnit.SECONDS, TIME_ADVANCE_SECONDS);
        // 格式化时间为字符串
        String formattedTime = DatetimeUtil.formatTime(advancedTime);
        // URL编码
        String encodedTime = UrlEncodeUtil.formatUrlParam(formattedTime);
        // 填充占位符
        return String.format(MONITOR_REPLAY_URL_TEMPLATE, encodedTime, vin);
    }
}