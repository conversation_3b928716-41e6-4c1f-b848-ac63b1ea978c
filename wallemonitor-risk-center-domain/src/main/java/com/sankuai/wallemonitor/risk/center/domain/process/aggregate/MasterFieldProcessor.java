package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.UserInfoRepository;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 组长字段处理器
 * 处理组长信息的显示逻辑
 */
@Component
@Slf4j
public class MasterFieldProcessor implements TemplateFieldProcessor {

    @Resource
    private UserInfoRepository userInfoRepository;

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {

        if (CollectionUtils.isEmpty(riskCaseList)) {
            template.setShowSecurityOfficer(false);
            template.setShowMster(true);
            template.setMaster("没有配置组长");
            return;
        }

        try {

            // 2. MIS -> 姓名
            List<String> masterMisList = alertTemplate.getMasterName();
            List<String> nameList = userInfoRepository.batchGetNamesByMis(masterMisList);

            // 3. MIS -> UID
            List<Long> uidList = userInfoRepository.batchGetUidsByMis(masterMisList);

            // 4. 构建@人格式
            List<String> atPersonList = buildAtPersonList(masterMisList, nameList, uidList);

            // 5. 拼接结果（多个组长用空格分隔）
            String masterText = String.join(" ", atPersonList);

            template.setMaster(masterText);
            template.setShowMster(true);
            template.setShowSecurityOfficer(false);

        } catch (Exception e) {
            log.error("处理组长字段失败", e);
            template.setShowMster(true);
            template.setShowSecurityOfficer(false);
            template.setMaster("没有配置组长");
        }
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.MASTER.getCode().equals(fieldCode);
    }
    

    /**
     * 构建@人格式列表
     */
    private List<String> buildAtPersonList(List<String> misList, List<String> nameList, List<Long> uidList) {
        List<String> result = new ArrayList<>();

        for (int i = 0; i < misList.size(); i++) {
            String mis = misList.get(i);
            String name = i < nameList.size() ? nameList.get(i) : mis;
            Long uid = i < uidList.size() ? uidList.get(i) : null;

            // 构建@人格式
            String uidParam = uid != null ? uid.toString() : "";
            String profileUrl = String.format("mtdaxiang://www.meituan.com/profile?uid=%s&isAt=true", uidParam);
            String atPersonFormat = String.format("[@%s|%s]", name, profileUrl);

            result.add(atPersonFormat);
        }

        return result;
    }
}
