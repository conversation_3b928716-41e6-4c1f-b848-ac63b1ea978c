package com.sankuai.wallemonitor.risk.center.domain.component;

import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.GeometryUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.MapElementRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.vo.HdMapElementGeoVO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter.SearchNearbyRequestVTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.FCDriveTaskRouteEventDTO.RoutePoint;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleInQueuePositionDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleObstacleInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.CrossLineWaitCheckConfig;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.DetectWaitQueueConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.ObstacleFineTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle.ObstacleType;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleCounterInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO.VehicleObstacleContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.CacheUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DetectorCommonCompute {

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private VehicleAdapter vehicleAdapter;


    /**
     * 地图类型
     */
    private static final String MAP_TYPE = "lane_polygon";
    /**
     * 地图搜索范围
     */
    private static final Double MAP_SEARCH_RANGE = 30.0;


    @ConfigValue(key = LionKeyConstant.LION_KEY_ERROR_QUEUE_DETECTOR_OBSTACLE_JUDGE_CONFIG, defaultValue = "{}")
    private DetectWaitQueueConfigDTO waitInQueueConfig;

    /**
     * 构建车道和障碍物的信息
     *
     * @param runtimeContextDO
     * @return
     */
    public VehicleLaneAndPositionComputeDTO buildVehicleLaneAndPosition(VehicleRuntimeInfoContextDO runtimeContextDO) {
        // 获取高精地图版本
        VehicleEveInfoVTO vehicleEveInfoVTO = CacheUtils.doCache(vehicleAdapter, 2, TimeUnit.MINUTES)
                .queryRuntimeVehicleInfoByVin(runtimeContextDO.getVin());
        String hdMapArea = Optional.ofNullable(vehicleEveInfoVTO).map(VehicleEveInfoVTO::findHdMapArea)
                .orElse(CharConstant.CHAR_EMPTY);
        if (StringUtils.isBlank(hdMapArea)) {
            log.error("buildVehicleLaneAndPosition hdMapArea is null, vin:{}", runtimeContextDO.getVin());
        }
        String vehicleType = Optional.ofNullable(vehicleEveInfoVTO).map(VehicleEveInfoVTO::getVehicleType)
                .orElse(CharConstant.CHAR_EMPTY);
        // 取停滞开始时刻
        Date startTime = Optional.ofNullable(runtimeContextDO.getStagnationCounter())
                .filter(vehicleCounterInfoDO -> !vehicleCounterInfoDO.isCountFinished())
                .map(VehicleCounterInfoDO::getStartTime).orElse(new Date());
        // 初始化计算上下文
        VehicleLaneAndPositionComputeDTO vehicleLaneAndPositionComputeDTO = VehicleLaneAndPositionComputeDTO.builder()
                // vin
                .vin(runtimeContextDO.getVin())
                // 高精地图版本
                .hdMapArea(hdMapArea)
                // 获取当前点
                .curPosition(runtimeContextDO.getLocation())
                // fcLaneIdList
                .fcLaneIdList(runtimeContextDO.getFcLaneIdList())
                // 车辆类型
                .vehicleType(vehicleType)
                // 规划线
                .refineLine(runtimeContextDO.getRefinedLineList())
                .build();
        // 构建定位
        buildVehiclePositionInfo(vehicleLaneAndPositionComputeDTO, startTime, waitInQueueConfig);
        // 构建车道
        buildVehicleLane(vehicleLaneAndPositionComputeDTO, waitInQueueConfig);
        // 构建障碍物
        buildObstacleInfo(vehicleLaneAndPositionComputeDTO, runtimeContextDO,
                waitInQueueConfig.getDistance2NearByAngle());
        return vehicleLaneAndPositionComputeDTO;
    }

    /**
     * 判断自车在指定条件下是否有障碍物
     *
     * @return
     */
    public Boolean isHasObstacle(VehicleLaneAndPositionComputeDTO computeDTO) {
        // 取障碍物
        if (Objects.isNull(computeDTO) || CollectionUtils.isEmpty(computeDTO.getObstacleInfoList())) {
            // 不存在任何障碍物信息
            return false;
        }
        // 判断前方是否有障碍物
        return computeDTO.getObstacleInfoList().stream()
                .anyMatch(vehicleObstacleInfoDTO -> isAheadObstacle(vehicleObstacleInfoDTO, waitInQueueConfig));
    }

    /**
     * 判断自车在指定条件下是否有障碍物(支持任意类型的障碍物)
     *
     * @return
     */
    public Boolean isHasObstacleV2(VehicleLaneAndPositionComputeDTO computeDTO, Double distanceThreshold,
            Double angleThreshold) {
        // 取障碍物
        if (Objects.isNull(computeDTO) || CollectionUtils.isEmpty(computeDTO.getObstacleInfoList())) {
            // 不存在任何障碍物信息
            return false;
        }
        // 判断前方是否有障碍物
        return computeDTO.getObstacleInfoList().stream()
                .anyMatch(vehicleObstacleInfoDTO -> isAheadObstacleV2(vehicleObstacleInfoDTO, distanceThreshold,
                        angleThreshold));
    }

    /**
     * 判断车辆是否压线
     *
     * @param computeDTO
     * @return
     */
    public Boolean isCrossLine(VehicleLaneAndPositionComputeDTO computeDTO) {
        if (Objects.isNull(computeDTO) || Objects.isNull(computeDTO.getDistanceToNearCurb())) {
            // 不存在任何短边信息
            return false;
        }
        CrossLineWaitCheckConfig crossLineWaitCheckConfig = waitInQueueConfig.getCrossLineWaitCheckConfig();
        // 3. 判断是否压线（即距离L是否小于车身的一半）
        return crossLineWaitCheckConfig.isCrossLine(computeDTO.getVehicleType(), computeDTO.getDistanceToNearCurb());
    }

    /**
     * 判断车辆是否在FC规划路由车道
     *
     * @param computeDTO
     * @return
     */
    public Boolean isInRefineLine(VehicleLaneAndPositionComputeDTO computeDTO) {
        if (Objects.isNull(computeDTO)
                || CollectionUtils.isEmpty(computeDTO.getCurLaneList())) {
            log.info("isInFCRouteLane 判断车辆是否在FC规划导航车道 参数为空");
            return false;
        }
        List<PositionDO> refineLinePosition = computeDTO.getRefineLine();
        if (CollectionUtils.isEmpty(refineLinePosition)) {
            log.info("isInFCRouteLane 判断车辆是否在FC规划路由车道 导航为空");
            return false;
        }
        List<HdMapElementGeoDO> lineList = new ArrayList<>();
        lineList.addAll(computeDTO.getCurLaneList());
        lineList.addAll(computeDTO.getVehicleAroundLane().getOrDefault(GeoElementTypeKeyConstant.SUCCESSOR,new ArrayList<>()));
        lineList.addAll(computeDTO.getVehicleAroundLane().getOrDefault(GeoElementTypeKeyConstant.PREDECESSOR,new ArrayList<>()));
        if (CollectionUtils.isEmpty(lineList)) {
            log.info("isInFCRouteLane 判断车辆是否在FC规划路由车道 当前车道为空");
            return false;
        }
        return lineList.stream()
                .anyMatch(hdMapElementGeoDO -> refineLinePosition.stream().anyMatch(hdMapElementGeoDO::isInPolygon));
    }


    /**
     * 是否为自车前方的障碍物
     *
     * @param obstacle
     * @param config
     * @return
     */
    public boolean isAheadObstacle(VehicleObstacleInfoDTO obstacle, DetectWaitQueueConfigDTO config) {
        if (Objects.isNull(obstacle) || Objects.isNull(config)) {
            return false;
        }
        boolean isAngleMatched = checkAngle(obstacle, config);
        // 判断距离是否满足
        Map<String, Double> distanceThresholdMap = config.getFineTypeDistanceThreshold();
        Double distanceThreshold = distanceThresholdMap.getOrDefault(obstacle.getFineType(),
                config.getDefaultDistanceThreshold());
        boolean isDistanceMatch = distanceThreshold != null && obstacle.getDistance() < distanceThreshold;

        List<String> fineTypeList = config.getFineTypeList();
        boolean isMatchedType = CollectionUtils.isNotEmpty(fineTypeList)
                && StringUtils.isNotBlank(obstacle.getFineType()) && fineTypeList.contains(obstacle.getFineType());

        boolean inCurLaneOrSuccessor = StringUtils.isNotBlank(obstacle.getLaneId())
                && (GeoElementTypeKeyConstant.SUCCESSOR.equals(obstacle.getLaneRelation2Vehicle())
                || GeoElementTypeKeyConstant.CUR.equals(obstacle.getLaneRelation2Vehicle()));

        return isAngleMatched && isDistanceMatch && isMatchedType && inCurLaneOrSuccessor;
    }

    /**
     * 是否为自车前方的障碍物
     *
     * @param obstacle
     * @return
     */
    public boolean isAheadObstacleV2(VehicleObstacleInfoDTO obstacle, Double distanceThreshold, Double angleThreshold) {
        if (Objects.isNull(obstacle) || Objects.isNull(distanceThreshold) || Objects.isNull(angleThreshold)) {
            return false;
        }
        // 判断夹角是否满足
        boolean isAngleMatched = obstacle.getMiddleAngle() != null && obstacle.getMiddleAngle() < angleThreshold;
        // 判断距离是否满足
        boolean isDistanceMatch = obstacle.getDistance() != null && obstacle.getDistance() < distanceThreshold;
        return isAngleMatched && isDistanceMatch;
    }

    /**
     * 检查障碍物与中心线的夹角是否满足条件
     *
     * @param obstacle
     * @param config
     * @return
     */
    private boolean checkAngle(VehicleObstacleInfoDTO obstacle, DetectWaitQueueConfigDTO config) {
        Map<String, Double> angleThresholdByObstacleType = config.getAngleThresholdByObstacleType();
        if (MapUtils.isEmpty(angleThresholdByObstacleType)) {
            return false;
        }
        Double defaultAngleThreshold = angleThresholdByObstacleType.get("DEFAULT");
        Double angleThreshold = angleThresholdByObstacleType.getOrDefault(obstacle.getObstacleType(),
                defaultAngleThreshold);
        return obstacle.getMiddleAngle() != null && obstacle.getMiddleAngle() < angleThreshold;
    }

    private void buildObstacleInfo(VehicleLaneAndPositionComputeDTO handContext,
            VehicleRuntimeInfoContextDO runtimeInfo, Double distance2NearByAngle) {
        PositionDO curPosition = handContext.getCurPosition();
        PositionDO preVehiclePosition = Optional.ofNullable(handContext.getPrePosition())
                .map(VehicleInQueuePositionDTO::getPosition).orElse(null);
        List<PerceptionObstacle> perceptionObstacleList = Optional.ofNullable(runtimeInfo.getObstacleContext())
                .map(VehicleObstacleContextDO::getPerceptionObstacle).orElse(new ArrayList<>());
        HdMapElementGeoDO vehicleCurLane = handContext.getVehicleCurLane();
        String hdMapArea = handContext.getHdMapArea();
        Pair<PositionDO, PositionDO> direction = handContext.getDirection();
        Map<String, List<HdMapElementGeoDO>> vehicleAroundLane = handContext.getVehicleAroundLane();
        // 遍历过程常量
        // 简化障碍物的信息
        List<VehicleObstacleInfoDTO> obstacleInfoList = perceptionObstacleList.stream()
                .filter(obstacle -> Objects.nonNull(obstacle) && Objects.nonNull(obstacle.getPosition()))
                .map(obstacle -> {
                    String fineType = Optional.ofNullable(obstacle.getObstacleType()).map(ObstacleType::getFineType)
                            .map(ObstacleFineTypeEnum::transferShotName).orElse(CharConstant.CHAR_EMPTY);
                    String obstacleType = Optional.ofNullable(obstacle.getObstacleType())
                            .map(ObstacleType::getCoarseType).orElse(CharConstant.CHAR_EMPTY);
                    // 计算utm区号
                    Integer zoneId = hdMapAdapter.getUtmZoneByArea(hdMapArea);
                    // 转换坐标系
                    PositionDO wgs84Position = GeoToolsUtil.utmToWgs84WithZone(obstacle.getPosition().getX(),
                            obstacle.getPosition().getY(), zoneId);
                    return VehicleObstacleInfoDTO.builder()
                            // 障碍物ID
                            .obstacleId(obstacle.getId())
                            // 坐标
                            .position(wgs84Position)
                            // 类型
                            .fineType(fineType).obstacleType(obstacleType).type(obstacle.getType())
                            // 宽度
                            .width(obstacle.getWidth()).build();
                }).collect(Collectors.toList());

        obstacleInfoList.forEach((obstacle) -> {
            // 和 障碍物连线 和 自车行径线夹角
            Double angle = GeoToolsUtil.angleCalc(curPosition, obstacle.getPosition(), preVehiclePosition, curPosition); // 计算障碍物夹角
            Double distance = GeoToolsUtil.distance(curPosition, obstacle.getPosition());
            obstacle.setDistance(distance); // 设置障碍物与当前车辆之间的距离。
            obstacle.setAngle(angle);
            // 获取离中心点最近的夹角，key是最近的一个，value是下一个
            Pair<PositionDO, PositionDO> laneCenterLine = Optional.ofNullable(vehicleCurLane)
                    .map(lane -> lane.getMiddleLineTwoNearPoint(curPosition)).orElse(null);
            // 车道中心线的夹角不为空时 算障碍物与中心线的夹角
            if (Objects.nonNull(laneCenterLine)) {
                Double middleAngle = GeoToolsUtil.angleCalc(curPosition, obstacle.getPosition(),
                        laneCenterLine.getKey(), laneCenterLine.getValue());
                obstacle.setMiddleAngle(middleAngle);
            }
            // 找到在车道内的或者后继车道的
            boolean inSameLane = vehicleCurLane != null && vehicleCurLane.isInPolygon(obstacle.getPosition());
            if (inSameLane) {
                // 只要在同车
                // 保存该障碍物所在的车道
                obstacle.updateLaneAndRelation(GeoElementTypeKeyConstant.CUR, vehicleCurLane);
                buildObstacleNearByDistance(obstacle, vehicleCurLane, direction, distance2NearByAngle);
            } else {
                // 本车周围的车道
                vehicleAroundLane.forEach((aroundType, hdGeoList) -> {
                    for (HdMapElementGeoDO hdGeo : hdGeoList) {
                        if (StringUtils.isNotBlank(obstacle.getLaneId())) {
                            // 已经找到车道，无需处理
                            break;
                        }
                        if (!hdGeo.isInPolygon(obstacle.getPosition())) {
                            continue;
                        }
                        // 如果是下个车道。使用下个车道的中心线
                        Pair<PositionDO, PositionDO> thisLanePair = hdGeo
                                .getMiddleLineTwoNearPoint(obstacle.getPosition());
                        // 如果这个在这个车道，则更新
                        obstacle.updateLaneAndRelation(aroundType, hdGeo);
                        // 计算障碍物相关距离
                        buildObstacleNearByDistance(obstacle, hdGeo, thisLanePair, distance2NearByAngle);
                    }
                });
            }
        });
        // 设置
        handContext.setObstacleInfoList(obstacleInfoList);

    }

    /**
     * 计算障碍物的最近距离
     *
     * @param obstacle
     * @param lane
     * @param direction
     * @param distance2NearByAngle
     */
    private void buildObstacleNearByDistance(VehicleObstacleInfoDTO obstacle, HdMapElementGeoDO lane,
            Pair<PositionDO, PositionDO> direction, Double distance2NearByAngle) {
        if (Objects.isNull(obstacle) || Objects.isNull(lane) || Objects.isNull(direction)
                || Objects.isNull(distance2NearByAngle)) {
            return;
        }
        // 车道平行线的夹角
        Pair<Double, Double> parallelSideDistance = GeoToolsUtil.pointToPolygonParallelSideDistance(
                obstacle.getPosition(), lane.getPolygonDO().getPoints(), direction, distance2NearByAngle);
        if (Objects.isNull(parallelSideDistance) || parallelSideDistance.getValue() == null
                || parallelSideDistance.getKey() == null) {
            // 如果为空
            return;
        }
        // 计算障碍物和边界的最小距离
        Double minDistance = Math.min(parallelSideDistance.getKey(), parallelSideDistance.getValue());
        // 设置值
        obstacle.setDistanceToNearCurb(minDistance);
        obstacle.setDistanceToNearCurbList(
                Lists.newArrayList(parallelSideDistance.getKey(), parallelSideDistance.getValue()));
    }

    /**
     * 构建车辆的车道信息
     *
     * @param preDataContextDTO
     * @param config
     */
    private void buildVehicleLane(VehicleLaneAndPositionComputeDTO preDataContextDTO, DetectWaitQueueConfigDTO config) {
        PositionDO curPosition = preDataContextDTO.getCurPosition();
        PositionDO preVehiclePosition = Optional.ofNullable(preDataContextDTO.getPrePosition())
                .map(VehicleInQueuePositionDTO::getPosition).orElse(null);
        if (Objects.isNull(preVehiclePosition)) {
            log.warn("buildVehicleLane preVehiclePosition is null");
        }
        // 查询车辆周围的车道信息
        SearchNearbyRequestVTO param = SearchNearbyRequestVTO.builder().hdMapEnum(HdMapEnum.LANE_POLYGON)
                .positionDO(curPosition).area(preDataContextDTO.getHdMapArea()).restrictType(config.getLaneTypeList())
                .range(config.getRange()).build();
        List<HdMapElementGeoDO> nearbyLaneList = hdMapAdapter.searchNearby(param);
        if (CollectionUtils.isEmpty(nearbyLaneList)) {
            log.warn("buildVehicleLane nearbyLaneList is empty, param = {}", JacksonUtils.to(param));
        }
        // 更新车道信息
        preDataContextDTO.setNearbyLaneList(nearbyLaneList);
        // 查询车辆所在的车道
        List<HdMapElementGeoDO> vehicleCurLaneList = nearbyLaneList.stream()
                // 确保车辆所在的车道
                .filter(hdMapElementGeoDO -> hdMapElementGeoDO.isInPolygon(preDataContextDTO.getCurPosition()))
                // 并且要同一个方向的
                .filter(hdMapElementGeoDO -> preVehiclePosition != null && hdMapElementGeoDO
                        .isSameDirection(preVehiclePosition, curPosition, config.getLaneSameDirectionTheta()))
                .collect(Collectors.toList());  // 找第最后一个 fixme: 这里可能会出现重叠车道
        if (CollectionUtils.isEmpty(vehicleCurLaneList)) {
            log.warn("buildVehicleLane vehicleCurLaneList is empty, nearbyLaneList = {}",
                    CollectionUtils.isEmpty(nearbyLaneList));
        }
        preDataContextDTO.setCurLaneList(vehicleCurLaneList);
        // 确定车辆当前所处车道,取和行径方向夹角最小的车道
        HdMapElementGeoDO vehicleCurLane = vehicleCurLaneList.stream().min(Comparator.comparing(lane -> {
            // 取最近的两个点
            Pair<PositionDO, PositionDO> direction = lane.getMiddleLineTwoNearPoint(curPosition);
            // 计算和行径线的夹角
            Double angle = GeoToolsUtil.angleCalc(preVehiclePosition, curPosition, direction.getKey(),
                    direction.getValue());
            // 返回进行比较
            return Optional.ofNullable(angle).orElse(Double.MAX_VALUE);
        })).orElse(null);
        // 更新周围车道
        Map<String, List<HdMapElementGeoDO>> vehicleAroundLane = getVehicleAroundLane(vehicleCurLaneList);
        if (Objects.isNull(vehicleCurLane)) {
            log.warn("buildVehicleLane vehicleCurLane is null, vehicleCurLaneList = {}",
                    CollectionUtils.isEmpty(vehicleCurLaneList));
        }

        // 取和车最近的中心线投影点，2个
        if (Objects.nonNull(vehicleCurLane)) {
            // 用中心线做两个点
            Pair<PositionDO, PositionDO> direction = vehicleCurLane.getMiddleLineTwoNearPoint(curPosition);
            // 和车道的最近距离 (排除不满足要求的距离)
            Pair<Double, Double> parallelSide = GeoToolsUtil.pointToPolygonParallelSideDistance(curPosition,
                    vehicleCurLane.getPolygonDO().getPoints(), direction, config.getDistance2NearByAngle());
            if (Objects.nonNull(parallelSide) && parallelSide.getKey() != null && parallelSide.getValue() != null) {
                Double distanceToNearCurb = Math.min(parallelSide.getKey(), parallelSide.getValue());
                preDataContextDTO.setDistanceToNearCurb(distanceToNearCurb);
                preDataContextDTO
                        .setDistanceToNearCurbList(Lists.newArrayList(parallelSide.getKey(), parallelSide.getValue()));
            }
            // 更新车辆的向量
            preDataContextDTO.setDirection(direction);
            // ---- 其他计算需要的上下文
            preDataContextDTO.setVehicleAroundLane(vehicleAroundLane);
            preDataContextDTO.setVehicleCurLane(vehicleCurLane);
            preDataContextDTO.updateLaneSelectionType(vehicleCurLane);
        }
    }

    /**
     * 获取车辆周围的车道
     *
     * @param vehicleCurLaneList
     * @return
     */
    private Map<String, List<HdMapElementGeoDO>> getVehicleAroundLane(List<HdMapElementGeoDO> vehicleCurLaneList) {
        if (CollectionUtils.isEmpty(vehicleCurLaneList)) {
            return new HashMap<>();
        }
        Map<String, List<HdMapElementGeoDO>> vehicleLaneMap = new HashMap<>();
        vehicleCurLaneList.forEach((vehicleCurLane) -> {
            List<String> predecessorLaneList = Optional.ofNullable(vehicleCurLane)
                    .map(lane -> (List<String>)lane.getPropertyByKey(GeoElementTypeKeyConstant.PREDECESSOR))
                    .orElse(new ArrayList<>());
            List<String> successorLaneList = Optional.ofNullable(vehicleCurLane)
                    .map(lane -> (List<String>)lane.getPropertyByKey(GeoElementTypeKeyConstant.SUCCESSOR))
                    .orElse(new ArrayList<>());
            String leftLane = Optional.ofNullable(vehicleCurLane)
                    .map(lane -> lane.getPropertyByKey(GeoElementTypeKeyConstant.LEFT)).map(String::valueOf)
                    .orElse(null);
            String rightLane = Optional.ofNullable(vehicleCurLane)
                    .map(lane -> lane.getPropertyByKey(GeoElementTypeKeyConstant.RIGHT)).map(String::valueOf)
                    .orElse(null);
            if (CollectionUtils.isNotEmpty(predecessorLaneList)) {
                // 前序车道不为空
                vehicleLaneMap.computeIfAbsent(GeoElementTypeKeyConstant.PREDECESSOR, k -> new ArrayList<>())
                        .addAll(hdMapAdapter.queryByLaneIdList(predecessorLaneList));
            }
            if (CollectionUtils.isNotEmpty(successorLaneList)) {
                // 后继车道不为空
                vehicleLaneMap.computeIfAbsent(GeoElementTypeKeyConstant.SUCCESSOR, k -> new ArrayList<>())
                        .addAll(hdMapAdapter.queryByLaneIdList(successorLaneList));
            }
            if (StringUtils.isNotBlank(leftLane)) {
                // 左车道不为空
                vehicleLaneMap.computeIfAbsent(GeoElementTypeKeyConstant.LEFT, k -> new ArrayList<>())
                        .addAll(hdMapAdapter.queryByLaneIdList(Lists.newArrayList(leftLane)));
            }
            if (StringUtils.isNotBlank(rightLane)) {
                // 右车道不为空
                vehicleLaneMap.computeIfAbsent(GeoElementTypeKeyConstant.RIGHT, k -> new ArrayList<>())
                        .addAll(hdMapAdapter.queryByLaneIdList(Lists.newArrayList(rightLane)));
            }
        });
        return vehicleLaneMap;
    }

    private void buildVehiclePositionInfo(VehicleLaneAndPositionComputeDTO runtimeContext, Date occurTime,
            DetectWaitQueueConfigDTO handleDataConfig) {

        PositionDO curPosition = runtimeContext.getCurPosition();
        Date timeBeforeOccurTime = DatetimeUtil.getNSecondsBeforeDateTime(occurTime, handleDataConfig.getPastSecond());
        // 查询过去的两个点
        List<VehicleInQueuePositionDTO> vehicleLastLocationList = vehicleAdapter
                .queryVehicleHistoryDataFromEveReplay(runtimeContext.getVin(),
                        DatetimeUtil.getTimeInSeconds(timeBeforeOccurTime), DatetimeUtil.getTimeInSeconds(occurTime))
                .stream().map(vehicleDataInfoVO -> {
                    PositionDO positionDO = PositionDO.getPosition(vehicleDataInfoVO.getLongitude(),
                            vehicleDataInfoVO.getLatitude(), CoordinateSystemEnum.WGS84);
                    return VehicleInQueuePositionDTO.builder().position(positionDO)
                            .distance(GeoToolsUtil.distance(positionDO, curPosition))     // 计算距离
                            .time(vehicleDataInfoVO.getTime())  // 时间
                            .build();
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehicleLastLocationList)) {
            log.warn("无法找到车辆历史定位信息");
            return;
        }
        Collections.reverse(vehicleLastLocationList);   // 直接倒序
        // 取最后一个不靠近定位点的点，防止有拐弯的情况
        VehicleInQueuePositionDTO prePositionDTO = IntStream.range(0, vehicleLastLocationList.size()).filter(index -> {
            VehicleInQueuePositionDTO position = vehicleLastLocationList.get(index);
            // 满足最小距离，防止过近引发异常
            return Objects.nonNull(handleDataConfig) && Objects.nonNull(position) && !position.invalid() // 定位合法
                    && Objects.nonNull(position.getDistance()) // 有距离
                    && position.getDistance() > handleDataConfig.getPreMinDistance();
        }).mapToObj(vehicleLastLocationList::get).findFirst().orElseGet(() -> vehicleLastLocationList.get(0));// 如果没找到指定的，就取最近的一个
        // 保存
        runtimeContext.setPrePosition(prePositionDTO);
        // 前序的position
        if (Objects.isNull(prePositionDTO)) {
            log.warn("无法找到就近的俩近似点");
        }

    }

    /**
     * 车道和定位的计算类
     */
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class VehicleLaneAndPositionComputeDTO {
        private PositionDO curPosition;
        @Default
        private List<PositionDO> refineLine = new ArrayList<>();

        private VehicleInQueuePositionDTO prePosition;
        @Default
        private List<HdMapElementGeoDO> curLaneList = new ArrayList<>();
        @Default
        private List<String> fcLaneIdList = new ArrayList<>();
        @Default
        private List<HdMapElementGeoDO> nearbyLaneList = new ArrayList<>();
        @Default
        private Map<String, List<HdMapElementGeoDO>> vehicleAroundLane = new HashMap<>();
        private HdMapElementGeoDO vehicleCurLane;
        private String vin;
        private String hdMapArea;
        private Pair<PositionDO, PositionDO> direction;
        private Double distanceToNearCurb;
        @Default
        private List<Double> distanceToNearCurbList = new ArrayList<>();
        @Default
        private List<VehicleObstacleInfoDTO> obstacleInfoList = new ArrayList<>();
        private String vehicleType;
        private String vehicleLaneSectionType;
        private Boolean singleLane;


        public void updateLaneSelectionType(HdMapElementGeoDO vehicleCurLane) {
            if (Objects.isNull(vehicleCurLane)) {
                return;
            }
            if (StringUtils.isBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.LEFT))
                    && StringUtils.isBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.RIGHT))) {
                this.vehicleLaneSectionType = "middle";
                this.setSingleLane(true);
            }
            boolean hasLeft = StringUtils.isNotBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.LEFT));
            boolean hasRight = StringUtils.isNotBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.RIGHT));
            // 如果有左、右，是中间
            // 如果有左没右，是最右
            // 如果有右没左，是最左
            if (hasLeft && hasRight) {
                this.vehicleLaneSectionType = "middle";
            } else if (hasLeft && !hasRight) {
                this.vehicleLaneSectionType = "right";
            } else if (!hasLeft && hasRight) {
                this.vehicleLaneSectionType = "left";
            }
        }

    }


    /**
     * 计算当前自车所在车道区域的点集
     *
     * @param runtimeContextDO
     * @return
     */
    public List<Coordinate> computeCurLaneArea(VehicleRuntimeInfoContextDO runtimeContextDO) {
        String hdMapArea = vehicleAdapter.getVehicleHdMapArea(runtimeContextDO.getVin());
        // 1 根据美团地图查询指定经纬度/范围内的地图元素
        MapElementRequestDTO laneQueryRequestDTO = MapElementRequestDTO.builder()
                .distance(MAP_SEARCH_RANGE)
                .mapType(MAP_TYPE)
                .hdMapVersion(hdMapArea)
                .longitude(Double.valueOf(runtimeContextDO.getLng()))
                .latitude(Double.valueOf(runtimeContextDO.getLat()))
                .build();
        List<HdMapElementGeoVO> elementGeoVOList = hdMapAdapter.searchMapElementByMapElementRequestDTO(
                laneQueryRequestDTO);
        if (CollectionUtils.isEmpty(elementGeoVOList)) {
            log.info("DetectorCommonCompute#computeCurLaneArea, elementGeoVOList is empty");
            return new ArrayList<>();
        }
        Coordinate coordinate = new Coordinate(laneQueryRequestDTO.getLongitude(),
                laneQueryRequestDTO.getLatitude());
        for (HdMapElementGeoVO geoVO : elementGeoVOList) {
            List<List<Double>> pointList = geoVO.getPoints();
            if (CollectionUtils.isEmpty(pointList)) {
                continue;
            }
            List<Coordinate> laneArea = pointList.stream().map(x -> new Coordinate(x.get(0), x.get(1)))
                    .collect(Collectors.toList());
            // 判断当前点所在的车道
            if (GeometryUtil.inPolygon(coordinate, laneArea)) {
                return laneArea;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 转换坐标格式
     *
     * @param routePoints
     * @return
     */
    private List<Coordinate> transformCoordinate(List<RoutePoint> routePoints) {
        if (CollectionUtils.isEmpty(routePoints)) {
            return new ArrayList<>();
        }
        return routePoints.stream().map(routePoint -> {
                    PositionDO positionDO = GeoToolsUtil.utmToWgs84(routePoint.getX(), routePoint.getY());
                    return new Coordinate(positionDO.getLongitude(), positionDO.getLatitude());
                })
                .collect(Collectors.toList());
    }
}
