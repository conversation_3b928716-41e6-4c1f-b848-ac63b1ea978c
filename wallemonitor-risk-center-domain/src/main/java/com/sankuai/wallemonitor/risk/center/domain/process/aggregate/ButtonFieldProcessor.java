package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 按钮字段处理器
 * 处理按钮相关属性设置
 */
@Component
@Slf4j
public class ButtonFieldProcessor implements TemplateFieldProcessor {

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {
        template.setIsBtnDisabled(false);
        template.setShowButton(true);
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.BUTTON.getCode().equals(fieldCode);
    }
} 