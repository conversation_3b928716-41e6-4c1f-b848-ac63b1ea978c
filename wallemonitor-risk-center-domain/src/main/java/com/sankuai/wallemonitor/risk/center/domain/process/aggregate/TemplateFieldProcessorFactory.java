package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;

/**
 * 模板字段处理器工厂
 * 管理和分发不同类型的字段处理器
 */
@Component
@Slf4j
public class TemplateFieldProcessorFactory {

    @Resource
    private List<TemplateFieldProcessor> fieldProcessors;

    private Map<String, TemplateFieldProcessor> processorMap;

    @PostConstruct
    public void initProcessorMap() {
        processorMap = fieldProcessors.stream()
                .collect(Collectors.toMap(
                        this::getProcessorKey,
                        processor -> processor,
                        (existing, replacement) -> {
                            log.warn("发现重复的字段处理器，保留现有的: {}", existing.getClass().getSimpleName());
                            return existing;
                        }
                ));

        log.info("初始化字段处理器完成，共注册 {} 个处理器", processorMap.size());
    }

    /**
     * 获取字段处理器
     *
     * @param fieldCode 字段编码
     * @return 字段处理器
     */
    public TemplateFieldProcessor getProcessor(String fieldCode) {
        return processorMap.get(fieldCode);
    }

    /**
     * 获取处理器的键值
     */
    private String getProcessorKey(TemplateFieldProcessor processor) {
        // 使用TemplateFieldEnum中的code值遍历所有可能的字段编码
        for (TemplateFieldEnum fieldEnum : TemplateFieldEnum.values()) {
            String code = fieldEnum.getCode();
            if (processor.supports(code)) {
                return code;
            }
        }

        log.warn("处理器未支持任何已知字段: {}", processor.getClass().getSimpleName());
        return processor.getClass().getSimpleName();
    }
} 