package com.sankuai.wallemonitor.risk.center.domain.process;


import com.google.common.collect.Lists;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleCounterInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDetectorRecordBaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 处理舆情风险检测记录的计时信息，变更存储至vehicle表
 */
@Slf4j
@Component
public class RiskDetectRecordCounterUpdatedTriggerProcess implements DomainEventProcess {

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private LockUtils lockUtils;

    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.RISK_DETECT_RECORD_COUNTER_UPDATED_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (!RiskDetectorRecordBaseDO.class.isAssignableFrom(eventDTO.getDomainClass())) {
            return true;
        }
        // 获取停滞发生变更的数据
        DomainEventChangeDTO<RiskDetectorRecordBaseDO> typedDomainEvent = (DomainEventChangeDTO<RiskDetectorRecordBaseDO>) eventDTO;
        List<RiskDetectorRecordBaseDO> stagnationCounterChanged = new ArrayList<>(typedDomainEvent.getBySingleField(
                //停滞信息发生变更
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "stagnationCounter")
                        //不是变更成空
                        && StringUtils.isNotBlank(entityFieldChangeRecordDTO.getAfter())
        ));

        if (CollectionUtils.isEmpty(stagnationCounterChanged)) {
            //如果没有停滞信息发生变更的。
            return true;
        }
        //找到每个case对应的积累信息
        Map<String, Pair<String, VehicleCounterInfoDO>> case2StagnationCounter = stagnationCounterChanged.stream()
                .collect(Collectors.toMap(RiskDetectorRecordBaseDO::getTmpCaseId,
                        riskDetectorRecordBaseDO -> new Pair<>(riskDetectorRecordBaseDO.getVin(),
                                riskDetectorRecordBaseDO.getStagnationCounter())));

        //更新风险上的停滞信息
        handleUpdateRiskCase(case2StagnationCounter);
        return true;
    }

    /**
     * 记录里面的每一段停滞
     *
     * @param case2StagnationCounter
     */
    private void handleUpdateRiskCase(
            Map<String, Pair<String, VehicleCounterInfoDO>> case2StagnationCounter) {
        if (MapUtils.isEmpty(case2StagnationCounter)) {
            return;
        }
        //取这些case对应的do，不过滤已完结的，只要存在，就去更新
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder()
                        .caseIdList(Lists.newArrayList(case2StagnationCounter.keySet()))
                        .build());
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return;
        }
        Set<String> caseIdList = riskCaseDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toSet());
        lockUtils.batchLockCanWait(LockKeyPreUtil.buildCaseCounter(caseIdList), () -> {
            //查询relation
            Map<String, List<RiskCaseVehicleRelationDO>> caseId2Relation = riskCaseVehicleRelationRepository.queryByParam(
                            RiderCaseVehicleRelationDOParamDTO.builder()
                                    .caseIdList(new ArrayList<>(caseIdList))
                                    .build()).stream()
                    .collect(Collectors.groupingBy(RiskCaseVehicleRelationDO::getCaseId, Collectors.toList()));
            caseId2Relation.forEach((caseId, relation) -> {
                Pair<String, VehicleCounterInfoDO> thisCaseVin2Counter = case2StagnationCounter.get(caseId);
                //更新
                relation.stream().filter(relationDO -> relationDO.getVin().equals(thisCaseVin2Counter.getKey()))
                        .forEach(relationDO -> relationDO.updateStagnationCount(thisCaseVin2Counter.getValue()));
                //保存
                riskCaseVehicleRelationRepository.batchSave(relation);
            });
        });

    }

}
