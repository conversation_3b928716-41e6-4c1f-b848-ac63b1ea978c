package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AggregateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.OperationDataRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.DxRichTextUtils;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.OperationDataVTO;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 泊位名称字段处理器
 * 需要查询泊位数据的复杂处理器
 */
@Component
@Slf4j
public class ParkingPlotNameFieldProcessor implements TemplateFieldProcessor {

    @Resource
    private OperationDataRepository operationDataRepository;

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {
        template.setShowParkingPlotName(true);
        if (CollectionUtils.isEmpty(riskCaseList)) {
            return;
        }

        Map<String, String> caseIdToParkingPlotId = context.getCaseIdToParkingPlotId();
        if (MapUtils.isEmpty(caseIdToParkingPlotId)) {
            template.setParkingPlotName(CommonConstant.UNKNOWN);
            return;
        }

        try {
            // 获取停车场ID到名称的映射
            Map<String, String> caseIdToParkingPlotName = buildCaseIdToParkingPlotNameMap(caseIdToParkingPlotId);

            List<String> parkingPlotNameList = buildParkingPlotNameList(riskCaseList, caseIdToParkingPlotName,
                    aggregateBy);
            template.setParkingPlotName(Joiner.on(", ").skipNulls().join(parkingPlotNameList));
        } catch (Exception e) {
            log.error("处理泊位名称字段失败", e);
            template.setParkingPlotName(CommonConstant.UNKNOWN);
        }
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.PARKING_PLOT_NAME.getCode().equals(fieldCode);
    }

    /**
     * 构建事件ID到停车场名称的映射
     */
    private Map<String, String> buildCaseIdToParkingPlotNameMap(Map<String, String> caseIdToParkingPlotId) {
        // 获取所有不重复的停车场ID
        List<String> parkingPlotIdList = caseIdToParkingPlotId.values().stream().distinct()
                .collect(Collectors.toList());

        // 查询停车场数据
        Map<String, OperationDataVTO> operationDataMap = operationDataRepository.getOperationDataByIds(
                parkingPlotIdList);

        // 构建事件ID到停车场名称的映射
        return caseIdToParkingPlotId.entrySet().stream().collect(Collectors.toMap(Entry::getKey, entry -> {
            OperationDataVTO data = operationDataMap.get(entry.getValue());
            return data != null ? data.getName() : CommonConstant.UNKNOWN;
        }, (v1, v2) -> v1));
    }

    /**
     * 构建泊位名称列表
     */
    private List<String> buildParkingPlotNameList(List<RiskCaseDO> riskCaseList,
            Map<String, String> caseIdToParkingPlotName, List<String> aggregateBy) {
        List<String> parkingPlotNameList = Lists.newArrayList();

        // 根据是否聚合决定取值策略
        if (CollectionUtils.isNotEmpty(aggregateBy) && aggregateBy.contains(
                AggregateFieldEnum.PARKING_PLOT_ID.getCode())) {
            // 聚合情况：只取第一个且加粗显示
            String parkingPlotName = StringUtils.defaultIfBlank(
                    caseIdToParkingPlotName.get(riskCaseList.get(0).getCaseId()), CommonConstant.UNKNOWN);
            String name = StringUtils.equals(parkingPlotName, CommonConstant.UNKNOWN) ? parkingPlotName
                    : DxRichTextUtils.toBold(parkingPlotName);
            parkingPlotNameList.add(name);
        } else {
            // 非聚合情况：取所有case的值
            riskCaseList.forEach(caseDO -> parkingPlotNameList.add(
                    StringUtils.defaultIfBlank(caseIdToParkingPlotName.get(caseDO.getCaseId()),
                            CommonConstant.UNKNOWN)));
        }

        return parkingPlotNameList;
    }
} 