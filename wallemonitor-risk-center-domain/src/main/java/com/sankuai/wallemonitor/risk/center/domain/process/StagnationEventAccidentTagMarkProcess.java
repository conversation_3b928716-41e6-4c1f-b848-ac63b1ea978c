package com.sankuai.wallemonitor.risk.center.domain.process;

import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.STAGNATION_EVENT_ACCIDENT_TAG_MARK_PROCESSOR_ENTRY;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.ImproperStrandingReason;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class StagnationEventAccidentTagMarkProcess implements DomainEventProcess {

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Resource
    private LockUtils lockUtils;

    /**
     * 处理事件
     *
     * @param eventDTO
     * @return
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(STAGNATION_EVENT_ACCIDENT_TAG_MARK_PROCESSOR_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != RiskCaseDO.class) {
            return true;
        }
        // 过滤出状态为终态的停滞事件
        List<RiskCaseDO> eventDOList = ((DomainEventChangeDTO<RiskCaseDO>) eventDTO).getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status")
                ).stream()
                .filter(item -> RiskCaseStatusEnum.isTerminal(item.getStatus()) && Objects.equals(item.getType(),
                        RiskCaseTypeEnum.STRANDING))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(eventDOList)) {
            log.info("StagnationEventAccidentTagMarkProcess#process 过滤后无事件");
            return true;
        }
        return handleProcessMsg(eventDOList);
    }

    /**
     * 处理消息
     *
     * @param eventDOList
     * @return
     */
    public boolean handleProcessMsg(List<RiskCaseDO> eventDOList) {
        // 1. 查询标注结果
        List<String> caseIdList = eventDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());
        Map<String, String> caseId2eventIdMap = eventDOList.stream()
                .collect(Collectors.toMap(RiskCaseDO::getCaseId, RiskCaseDO::getEventId));
        CaseMarkInfoDOQueryParamDTO paramDTO = CaseMarkInfoDOQueryParamDTO.builder()
                .caseIdList(caseIdList).build();
        List<CaseMarkInfoDO> caseMarkInfoDOS = caseMarkInfoRepository.queryByParam(paramDTO);
        if (CollectionUtils.isEmpty(caseMarkInfoDOS)) {
            log.info("StagnationEventAccidentTagMarkProcess#handleProcessMsg 过滤后无事件");
            return true;
        }
        // 2. 对标注结果进行复检
        List<CaseMarkInfoDO> updateCaseMarkInfoDOS = new ArrayList<>();
        caseMarkInfoDOS.stream().filter(item -> StringUtils.isNotBlank(item.getFirstCategory())).forEach(item -> {
            ImproperStrandingReason improperStrandingReason = item.getImproperStrandingReason();
            // 只关注事故状态
            if (Objects.isNull(improperStrandingReason) || Objects.isNull(
                    improperStrandingReason.getWithAccidentOrder())) {
                return;
            }
            // 更新标注结果
            item.setFirstCategory(ISCheckCategoryEnum.ACCIDENT_HANDLE.getCategory());
            item.setFirstSubCategory(ISCheckCategoryEnum.ACCIDENT_HANDLE.getSubcategory());
            updateCaseMarkInfoDOS.add(item);
        });
        if (CollectionUtils.isEmpty(updateCaseMarkInfoDOS)) {
            return true;
        }

        // 3.更新检核结果
        try {
            Set<String> eventIdList = updateCaseMarkInfoDOS.stream()
                    .map(item -> caseId2eventIdMap.get(item.getCaseId())).collect(Collectors.toSet());
            // todo: 这里的更新感觉不上锁也没问题，上锁的话还要去查对应停滞事件的eventId
            lockUtils.batchLockNoWait(eventIdList, () -> {
                caseMarkInfoRepository.batchSave(updateCaseMarkInfoDOS);
            });
            return true;
        } catch (Exception e) {
            log.error("handleQueueItemIntoChecking error", e);
            return false;
        }
    }
}
