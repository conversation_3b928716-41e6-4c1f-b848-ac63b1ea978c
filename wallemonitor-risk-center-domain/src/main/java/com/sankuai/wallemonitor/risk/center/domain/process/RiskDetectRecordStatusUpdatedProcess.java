package com.sankuai.wallemonitor.risk.center.domain.process;


import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetectorManager;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDetectorRecordBaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskBaseRecordDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleBasicVTO;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 处理舆情风险检测记录「已确认为风险」
 */
@Slf4j
@Component
public class RiskDetectRecordStatusUpdatedProcess implements DomainEventProcess {

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private IDGenerateRepository idGenerateRepository;

    @Resource
    private RiskDetectorManager riskDetectorManager;


    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.RISK_DETECT_RECORD_STATUS_UPDATED_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (!RiskDetectorRecordBaseDO.class.isAssignableFrom(eventDTO.getDomainClass())) {
            return true;
        }
        // 从事件DTO中获取状态变更的风险案例列表，过滤出状态为 确认风险|已取消 的事件
        DomainEventChangeDTO<RiskDetectorRecordBaseDO> typedDomainEvent = (DomainEventChangeDTO<RiskDetectorRecordBaseDO>) eventDTO;
        List<RiskDetectorRecordBaseDO> confirmedRecordList = new ArrayList<>();
        List<RiskDetectorRecordBaseDO> canceledRecordList = new ArrayList<>();
        typedDomainEvent.getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"))
                .forEach(item -> {
                    if (DetectRecordStatusEnum.isConfirmed(item.getStatus())) {
                        confirmedRecordList.add(item);
                    }
                    if (DetectRecordStatusEnum.isCancel(item.getStatus())) {
                        canceledRecordList.add(item);
                    }
                });

        if (CollectionUtils.isNotEmpty(confirmedRecordList)) {
            //存在被确认的
            handleDetectConfirm(confirmedRecordList);
        }
        if (CollectionUtils.isNotEmpty(canceledRecordList)) {
            //存在被取消的
            handleDetectRecordCanceledWithLock(canceledRecordList);
        }
        return true;
    }

    /**
     * 处理状态变更为完结态的记录
     *
     * @param recordList
     * @return
     */
    public Boolean handleDetectConfirm(List<RiskDetectorRecordBaseDO> recordList) {
        log.info("handleDetectConfirm recordList: {}", JacksonUtils.to(recordList));
        Map<String, VehicleBasicVTO> vin2VehicleBasicVTO = vehicleAdapter.queryVehicleBasicInfoList(
                        recordList.stream().map(RiskDetectorRecordBaseDO::getVin).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(VehicleBasicVTO::getVin, item -> item, (o1, o2) -> o1));
        // 计算caseId到eventId的转换
        Map<String, String> case2EventId = recordList.stream()
                // 必须有对应的车辆
                .filter(record -> vin2VehicleBasicVTO.containsKey(record.getVin()))
                // 取caseId为key,value是vehicleName
                .collect(Collectors.toMap(RiskDetectorRecordBaseDO::getTmpCaseId, record -> {
                    // 构建eventId返回，vin2VehicleBasicVTO一定包含车辆
                    return idGenerateRepository.generateEventId(record.getOccurTime(), record.getType().name(),
                            vin2VehicleBasicVTO.get(record.getVin()).getVehicleName());
                }, (o1, o2) -> o1));
        // 构建caseId
        Set<String> eventId = new HashSet<>(case2EventId.values());
        lockUtils.batchLockCanWait(
                LockKeyPreUtil.buildKeyWithEventId(eventId),
                () -> {
                    List<RiskDetectorRecordBaseDO> failedList = runInLock(recordList, vin2VehicleBasicVTO,
                            case2EventId);
                    if (CollectionUtils.isNotEmpty(failedList)) {
                        log.error("handleDetectConfirm failedList: {}", JacksonUtils.to(failedList));
                        throw new RuntimeException("handleDetectConfirm failed");
                    }
                });
        return true;
    }

    /**
     * @param recordList
     * @param vin2VehicleBasicVTO
     * @param case2EventId
     */
    private List<RiskDetectorRecordBaseDO> runInLock(List<RiskDetectorRecordBaseDO> recordList,
            Map<String, VehicleBasicVTO> vin2VehicleBasicVTO, Map<String, String> case2EventId) {
        List<String> noCanceledCaseIdList = filterNoCanceledCaseIdList(recordList);
        List<RiskDetectorRecordBaseDO> failed = new ArrayList<>();
        recordList.stream().filter(x -> noCanceledCaseIdList.contains(x.getTmpCaseId())).forEach(record -> {
            try {
                //查询车辆信息
                VehicleBasicVTO vehicleBasicVTO = vin2VehicleBasicVTO.get(record.getVin());
                CheckUtil.isNotNull(vehicleBasicVTO, "查询不到车辆信息");
                // 使用停滞的开始时间，来设置eventId
                String eventId = case2EventId.get(record.getTmpCaseId());
                RiskCaseUpdatedParamDTO riskCaseUpdatedParamDTO = RiskCaseUpdatedParamDTO.builder()
                        .eventId(eventId)
                        .caseId(record.getTmpCaseId())
                        .vinList(Lists.newArrayList(record.getVin()))
                        .source(RiskCaseSourceEnum.BEACON_TOWER)
                        .type(record.getType())
                        .status(DetectRecordStatusEnum.convertToRiskCaseStatus(record.getStatus()))
                        .timestamp(record.getOccurTime().getTime())
                        .recallTime(Optional.ofNullable(record.getRecallTime()).map(Date::getTime).orElse(null))
                        .build();
                riskCaseOperateService.createOrUpdateRiskCase(riskCaseUpdatedParamDTO);
            } catch (Exception e) {
                log.error(String.format("riskCaseOperateService.createOrUpdateRiskCase failed, record: %s",
                        JacksonUtils.to(record)), e);
                failed.add(record);
            }
        });
        return failed;
    }

    /**
     * 根据不同类型record使用对应的repo过滤出未结束的case
     *
     * @param
     * @return
     */
    private List<String> filterNoCanceledCaseIdList(List<RiskDetectorRecordBaseDO> recordList) {
        Map<RiskCaseTypeEnum, List<RiskDetectorRecordBaseDO>> recordGroups = recordList.stream()
                .collect(Collectors.groupingBy(RiskDetectorRecordBaseDO::getType));
        return recordGroups.entrySet().stream().flatMap(entry -> {
            List<String> caseIdList = entry.getValue().stream()
                    .map(RiskDetectorRecordBaseDO::getTmpCaseId)
                    .collect(Collectors.toList());
            RiskDetectorRecordBaseDO riskDetectorRecordBaseDO = entry.getValue().get(0);
            AbstractMapperSingleRepository repo = riskDetectorManager
                    .getRepositoryByType(riskDetectorRecordBaseDO.getClass());
            if (Objects.isNull(repo)) {
                return Stream.empty();
            }
            List<RiskDetectorRecordBaseDO> unFinished = repo
                    .queryByParam(RiskBaseRecordDOQueryParamDTO.builder().tmpCaseIdList(caseIdList)
                            // 保留未取消的
                            .statusList(DetectRecordStatusEnum.getUnCancel()).build());
            return unFinished.stream().map(RiskDetectorRecordBaseDO::getTmpCaseId);
        }).collect(Collectors.toList());
    }

    /**
     * 处理状态变更为取消的记录(带锁处理)
     *
     * @param recordList
     * @return
     */
    public Boolean handleDetectRecordCanceledWithLock(List<RiskDetectorRecordBaseDO> recordList) {
        List<String> tmpCaseIdList = recordList.stream()
                .map(RiskDetectorRecordBaseDO::getTmpCaseId)
                .collect(Collectors.toList());

        // 对所有待处理的eventId加锁，避免创建和取消消息并发处理
        // todo: handleDetectRecordCanceled 是对 RiskCase 的更新，应该使用 eventId
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().caseIdList(tmpCaseIdList).build());
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            log.info("handleDetectRecordCanceledWithLock riskCaseDOList is empty");
            return true;
        }
        List<String> eventIdList = riskCaseDOList.stream().map(RiskCaseDO::getEventId).collect(Collectors.toList());
        return lockUtils.batchLockCanWait(
                LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(eventIdList)),
                () -> handleDetectRecordCanceled(recordList));
    }

    /**
     * 处理状态变更为取消的记录
     *
     * @param recordList
     * @return
     */
    public Boolean handleDetectRecordCanceled(List<RiskDetectorRecordBaseDO> recordList) {
        //查询case对应的id
        List<String> tmpCaseIdList = recordList.stream().map(RiskDetectorRecordBaseDO::getTmpCaseId)
                .collect(Collectors.toList());
        log.info("handleDetectRecordCanceled tmpCaseIdList: {}", JacksonUtils.to(tmpCaseIdList));
        //查询未结束的
        // todo: 边界case - 风险case的创建和取消消息间隔时间很短，此时直接查是查不到的，提前返回导致取消信号丢失，风险时间无法被结束
        //        修改方式 - 在处理消息之间进行加锁，如果此时正在创建case则会加锁失败，抛出异常后触发消息重试
        List<RiskCaseDO> unFinished = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder()
                        .statusList(Lists.newArrayList(RiskCaseStatusEnum.getUnTerminal()))
                        .caseIdList(tmpCaseIdList).build());
        if (CollectionUtils.isEmpty(unFinished)) {
            log.info("handleDetectRecordCanceled unFinished is empty");
            return true;
        }
        Set<String> unFinishedCaseIdSet = new HashSet<>();
        Map<String, String> caseId2EventIdMap = new HashMap<>();
        unFinished.forEach(item -> {
            unFinishedCaseIdSet.add(item.getCaseId());
            caseId2EventIdMap.put(item.getCaseId(), item.getEventId());
        });
        log.info("handleDetectRecordCanceled unFinishedCaseIdSet: {}", JacksonUtils.to(unFinishedCaseIdSet));
        // fixme: 修改后只要有一个失败就会进行重试，极端情况会导致整批取消信号全部丢失（目前每次只有一个）
        recordList.stream()
                //必须在未结束的case里面，才能取消
                .filter(record -> unFinishedCaseIdSet.contains(record.getTmpCaseId()))
                .forEach(record -> {
                        String eventId = caseId2EventIdMap.get(record.getTmpCaseId());
                        RiskCaseUpdatedParamDTO riskCaseUpdatedParamDTO = RiskCaseUpdatedParamDTO.builder()
                                .eventId(eventId)
                                .caseId(record.getTmpCaseId())
                                .vinList(Lists.newArrayList(record.getVin()))
                                .source(RiskCaseSourceEnum.BEACON_TOWER)
                                .type(record.getType())
                                .status(DetectRecordStatusEnum.convertToRiskCaseStatus(record.getStatus()))
                                .timestamp(record.getCloseTime().getTime())
                                .recallTime(Optional.ofNullable(record.getRecallTime()).map(Date::getTime).orElse(null))
                                .build();
                    riskCaseOperateService.createOrUpdateRiskCase(riskCaseUpdatedParamDTO);
                });
        return true;
    }
}
