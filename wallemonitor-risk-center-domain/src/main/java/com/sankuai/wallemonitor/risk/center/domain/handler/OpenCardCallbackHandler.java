package com.sankuai.wallemonitor.risk.center.domain.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordLabelEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskAlertRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.UserIdentityRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskAlertRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxCardUpdateParamVTO;
import com.sankuai.xm.openplatform.api.entity.CardCallBackReq;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 开放平台卡片回调处理器
 */
@Service
@Slf4j
public class OpenCardCallbackHandler {

    @Resource
    private UserIdentityRepository userIdentityRepository;

    @Resource
    private RiskAlertRecordRepository riskAlertRecordRepository;

    @Resource
    private DxNoticeAdapter dxNoticeAdapter;

    /**
     * 处理卡片回调请求，卡片操作会额外插入一条操作记录而不是直接更新旧的操作记录
     *
     * @param cardCallBackReq 卡片回调请求
     */
    public void handle(CardCallBackReq cardCallBackReq) {
        if (!isValid(cardCallBackReq)) {
            log.warn("卡片回调请求参数无效");
            return;
        }

        String messageId = cardCallBackReq.getRequestParam().getRequestId();
        RiskAlertRecordDO originalRecord = findRecordByMessageId(messageId);
        if (originalRecord == null) {
            return;
        }
        // 创建新的操作记录
        RiskAlertRecordDO newRecord = createNewRecord(originalRecord, cardCallBackReq);
        // 更新操作人
        updateOperator(cardCallBackReq, newRecord);
        // 处理回调参数并更新记录
        processCallbackParams(newRecord, cardCallBackReq.getInteractiveParam());
        // 更新卡片
        updateCard(newRecord);
        // 保存记录
        riskAlertRecordRepository.save(newRecord);

        log.info("卡片回调请求处理完成, messageId: {}", messageId);
    }

    /**
     * 异步处理卡片回调请求
     *
     * @param cardCallBackReq
     */
    public void asyncHandle(CardCallBackReq cardCallBackReq) {
        try {
            ParallelExecutor.executeAsyncTask("dx_callback_handler", () -> handle(cardCallBackReq));
        } catch (Exception e) {
            log.error("异步处理卡片回调请求失败", e);
        }
    }

    /**
     * 根据消息ID查找记录
     */
    private RiskAlertRecordDO findRecordByMessageId(String messageId) {
        RiskAlertRecordDO record = riskAlertRecordRepository.getLatestRecordByMessageId(messageId);
        if (record == null) {
            log.warn("未找到对应的记录, messageId: {}", messageId);
        }
        return record;
    }

    /**
     * 创建新的操作记录
     */
    private RiskAlertRecordDO createNewRecord(RiskAlertRecordDO originalRecord, CardCallBackReq cardCallBackReq) {
        // 复制原记录属性，创建新的操作记录
        RiskAlertRecordDO newRecord = new RiskAlertRecordDO();
        BeanUtils.copyProperties(originalRecord, newRecord);
        newRecord.setId(null);
        newRecord.setCreateTime(new Date());
        newRecord.setUpdateTime(new Date());
        return newRecord;
    }

    /**
     * 处理回调参数
     */
    private void processCallbackParams(RiskAlertRecordDO record, String interactiveParam) {
        if (StringUtils.isBlank(interactiveParam)) {
            log.warn("交互参数为空");
            return;
        }

        try {
            Map<String, String> paramMap = JacksonUtils.from(interactiveParam,
                    new TypeReference<Map<String, String>>() {
                    });
            String operateType = paramMap.get("operateType");
            String voteType = paramMap.get("voteType");
            updateStatus(record, operateType);
            updateLabel(record, voteType);
        } catch (Exception e) {
            log.error("处理回调参数失败", e);
        }
    }

    private void updateOperator(CardCallBackReq cardCallBackReq, RiskAlertRecordDO record) {
        Long empId = cardCallBackReq.getOperator().getEmpId();
        String mis = Optional.ofNullable(userIdentityRepository.getMisByEmpId(empId))
                .filter(StringUtils::isNotBlank)
                .orElse(CommonConstant.UNKNOWN);
        record.setOperator(mis);
    }

    private void updateCard(RiskAlertRecordDO record) {
        DxCardUpdateParamVTO dxCardUpdateParam = DxCardUpdateParamVTO.builder()
                .arguments(record.getArguments())
                .messageId(record.getMessageId())
                .version(System.currentTimeMillis())
                .build();
        dxNoticeAdapter.updateDxCard(dxCardUpdateParam);
    }

    /**
     * 根据参数更新状态
     */
    private void updateStatus(RiskAlertRecordDO record, String operateType) {
        if (StringUtils.isBlank(operateType) || !NumberUtils.isNumber(operateType)) {
            return;
        }
        try {
            int statusCode = NumberUtils.toInt(operateType);
            AlertRecordStatusEnum statusEnum = AlertRecordStatusEnum.findByCode(statusCode);
            if (Objects.isNull(statusEnum)) {
                return;
            }
            DxCardTemplateDO dxCardTemplateDO = JacksonUtils.from(record.getArguments(),
                    new TypeReference<DxCardTemplateDO>() {
                    });
            dxCardTemplateDO.updateByStatus(statusEnum);
            record.setStatus(statusEnum);
            record.setArguments(dxCardTemplateDO.toArguments());
            log.info("更新状态: {}", statusEnum);
        } catch (Exception e) {
            log.warn("更新状态失败! ", e);
        }
    }

    /**
     * 根据参数更新标签
     */
    private void updateLabel(RiskAlertRecordDO record, String voteType) {
        if (StringUtils.isBlank(voteType)) {
            return;
        }

        try {
            AlertRecordLabelEnum labelEnum = AlertRecordLabelEnum.findByVoteType(voteType);
            if (Objects.isNull(labelEnum)) {
                return;
            }
            DxCardTemplateDO dxCardTemplateDO = JacksonUtils.from(record.getArguments(),
                    new TypeReference<DxCardTemplateDO>() {
                    });
            dxCardTemplateDO.updateFeedbackByLabel(labelEnum);
            record.setLabel(labelEnum);
            record.setArguments(dxCardTemplateDO.toArguments());
        } catch (Exception e) {
            log.warn("更新标签失败: {}", voteType, e);
        }
    }

    private boolean isValid(CardCallBackReq cardCallBackReq) {
        return Objects.nonNull(cardCallBackReq)
                && Objects.nonNull(cardCallBackReq.getOperator())
                && Objects.nonNull(cardCallBackReq.getRequestParam())
                && StringUtils.isNotBlank(cardCallBackReq.getRequestParam().getRequestId());
    }
}