package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 提示词字段处理器
 * 处理提示词的显示逻辑
 */
@Component
@Slf4j
public class PromptFieldProcessor implements TemplateFieldProcessor {

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {
        
        // 从配置中获取提示词
        String promptText = getPromptFromConfig(alertTemplate);
        
        if (StringUtils.isBlank(promptText)) {
            log.warn("提示词为空，不显示提示词字段");
            template.setShowPrompt(true);
            template.setPrompt("没有配置提示词");
            return;
        }
        
        template.setPrompt(promptText);
        template.setShowPrompt(true);
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.PROMPT.getCode().equals(fieldCode);
    }

    /**
     * 从配置中获取提示词
     */
    private String getPromptFromConfig(AlertTemplateConfigDTO alertTemplate) {
        if (alertTemplate == null || StringUtils.isBlank(alertTemplate.getPromptText())) {
            return null;
        }
        return alertTemplate.getPromptText();
    }
}
