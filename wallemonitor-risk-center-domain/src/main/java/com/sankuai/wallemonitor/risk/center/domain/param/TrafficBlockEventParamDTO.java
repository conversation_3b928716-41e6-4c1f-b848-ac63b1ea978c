package com.sankuai.wallemonitor.risk.center.domain.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 堵路事件参数DTO
 * 用于在各层之间传递堵路事件相关参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TrafficBlockEventParamDTO {

    /**
     * 事件ID
     */
    private String caseId;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 发生时间
     */
    private LocalDateTime occurTime;

    /**
     * 解除时间
     */
    private LocalDateTime closeTime;

    /**
     * 纬度 (GCJ02坐标系)
     */
    private Double latitude;

    /**
     * 经度 (GCJ02坐标系)
     */
    private Double longitude;

    /**
     * 扩展信息
     */
    private Object extInfo;

    /**
     * 消息时间戳
     */
    private Long timestamp;

    /**
     * 匹配到的停滞不当事件ID（处理后填充）
     */
    private String matchedStrandingCaseId;


}
