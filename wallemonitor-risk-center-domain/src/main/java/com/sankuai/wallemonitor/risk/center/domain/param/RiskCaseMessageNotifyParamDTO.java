package com.sankuai.wallemonitor.risk.center.domain.param;

import com.sankuai.wallemonitor.risk.center.domain.process.RiskCaseMessageNoticeProcess.MessageCreateOrUpdatedDTO;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCaseMessageNotifyParamDTO {



    /**
     * 需要更新或者
     */
    @Builder.Default
    private List<MessageCreateOrUpdatedDTO> messageCreateOrUpdatedDTOList = new ArrayList<>();



    /**
     * 消息版本
     */
    @Builder.Default
    private Long messageVersion = System.currentTimeMillis();

}
